# 💳 Configuración de Stripe para Pagos Seguros

## 🚀 ¿Qué hemos implementado?

### **Características del Sistema de Pagos**
- ✅ **Tarjetas de crédito/débito**: Visa, Mastercard, American Express
- ✅ **PayPal**: Integración nativa a través de Stripe
- ✅ **Seguridad PCI DSS**: Manejada automáticamente por Stripe
- ✅ **3D Secure**: Autenticación adicional para Europa
- ✅ **Suscripciones recurrentes**: Renovación automática mensual
- ✅ **Webhooks**: Notificaciones automáticas de eventos
- ✅ **Impuestos automáticos**: Cálculo según ubicación del cliente

## 🔧 Configuración Paso a Paso

### **1. Crear Cuenta en Stripe**

1. Ve a [stripe.com](https://stripe.com)
2. Haz clic en "Sign up"
3. Crea tu cuenta empresarial
4. Completa la verificación de identidad

### **2. Obtener API Keys**

1. Ve al **Dashboard de Stripe**
2. En el menú lateral: **Developers** → **API keys**
3. Copia las siguientes keys:
   - **Publishable key** (empieza con `pk_test_`)
   - **Secret key** (empieza con `sk_test_`)

### **3. Configurar Variables de Entorno**

Edita `.env.local` y reemplaza los placeholders:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_tu_secret_key_aqui
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_tu_publishable_key_aqui
STRIPE_WEBHOOK_SECRET=whsec_tu_webhook_secret_aqui
```

### **4. Crear Productos y Precios en Stripe**

#### **4.1 Crear Productos**

1. En Stripe Dashboard: **Products** → **Add product**
2. Crear dos productos:

**Producto 1: Plan Usuario**
- Name: `OposiAI Plan Usuario`
- Description: `Plan mensual con funciones avanzadas`

**Producto 2: Plan Pro**
- Name: `OposiAI Plan Pro`
- Description: `Plan mensual con todas las funciones`

#### **4.2 Configurar Precios**

Para cada producto, crear un precio:

**Plan Usuario:**
- Price: `€9.99`
- Billing period: `Monthly`
- Price ID: Copiar el ID generado (ej: `price_1234567890`)

**Plan Pro:**
- Price: `€19.99`
- Billing period: `Monthly`
- Price ID: Copiar el ID generado (ej: `price_0987654321`)

#### **4.3 Actualizar Configuración**

Edita `src/lib/stripe/config.ts` y actualiza los `stripePriceId`:

```typescript
export const PLANS = {
  // ...
  usuario: {
    // ...
    stripePriceId: 'price_TU_PRICE_ID_USUARIO_AQUI',
  },
  pro: {
    // ...
    stripePriceId: 'price_TU_PRICE_ID_PRO_AQUI',
  }
}
```

### **5. Configurar Webhook**

#### **5.1 Crear Endpoint de Webhook**

1. En Stripe Dashboard: **Developers** → **Webhooks**
2. Haz clic en **Add endpoint**
3. URL: `https://tu-dominio.com/api/stripe/webhook`
4. Seleccionar eventos:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

#### **5.2 Obtener Webhook Secret**

1. Después de crear el webhook, copia el **Signing secret**
2. Actualiza `.env.local`:
   ```env
   STRIPE_WEBHOOK_SECRET=whsec_tu_webhook_secret_aqui
   ```

### **6. Configurar PayPal (Opcional)**

1. En Stripe Dashboard: **Settings** → **Payment methods**
2. Habilitar **PayPal**
3. Seguir las instrucciones para conectar tu cuenta de PayPal

## 🧪 Probar el Sistema

### **1. Modo de Prueba**

Stripe proporciona tarjetas de prueba:

```
Tarjeta exitosa: 4242 4242 4242 4242
Tarjeta que falla: 4000 0000 0000 0002
Tarjeta 3D Secure: 4000 0025 0000 3155
```

### **2. Flujo de Prueba**

1. **Reiniciar servidor**: `npm run dev`
2. **Ir a**: `http://localhost:3001`
3. **Seleccionar plan** Usuario o Pro
4. **Completar pago** con tarjeta de prueba
5. **Verificar webhook** en logs del servidor
6. **Verificar email** de notificación

## 📧 Notificaciones Automáticas

El sistema envía emails automáticos a `<EMAIL>` para:

- ✅ **Pagos exitosos** con detalles del cliente
- ❌ **Pagos fallidos** para seguimiento
- 🔄 **Nuevas suscripciones** creadas
- 🚫 **Suscripciones canceladas**

## 🔒 Seguridad

### **Características de Seguridad Implementadas**

- **PCI DSS Compliance**: Stripe maneja todos los datos de tarjetas
- **SSL/TLS**: Todas las comunicaciones encriptadas
- **Webhook Verification**: Verificación de firma para webhooks
- **3D Secure**: Autenticación adicional cuando es requerida
- **Fraud Detection**: Detección automática de fraude por Stripe

### **Mejores Prácticas**

- ✅ **Nunca** almacenar datos de tarjetas en tu servidor
- ✅ **Siempre** verificar webhooks con la firma
- ✅ **Usar HTTPS** en producción
- ✅ **Mantener** las API keys seguras

## 🚀 Despliegue en Producción

### **1. Cambiar a Modo Live**

1. En Stripe Dashboard, cambiar de **Test** a **Live**
2. Obtener nuevas API keys de producción
3. Actualizar variables de entorno de producción
4. Crear nuevos webhooks para la URL de producción

### **2. Configurar Dominio**

Actualizar `NEXT_PUBLIC_APP_URL` en producción:
```env
NEXT_PUBLIC_APP_URL=https://tu-dominio-real.com
```

## 📊 Monitoreo

### **Dashboard de Stripe**

- **Payments**: Ver todos los pagos
- **Subscriptions**: Gestionar suscripciones
- **Customers**: Ver información de clientes
- **Webhooks**: Monitorear eventos
- **Logs**: Debugging de problemas

### **Métricas Importantes**

- Tasa de conversión de checkout
- Pagos fallidos
- Cancelaciones de suscripción
- Revenue mensual recurrente (MRR)

## 🆘 Solución de Problemas

### **Error: "Invalid API key"**
- Verificar que la API key sea correcta
- Asegurar que esté en el modo correcto (test/live)

### **Webhook no funciona**
- Verificar la URL del webhook
- Comprobar que el secret sea correcto
- Revisar logs del servidor

### **Pago rechazado**
- Verificar fondos en la tarjeta
- Comprobar datos de la tarjeta
- Revisar restricciones del banco

¡El sistema de pagos está listo para procesar transacciones seguras! 🎉
