// src/app/api/stripe/create-checkout-session/route.ts
import { NextResponse } from 'next/server';
import { stripe, PLANS, getPlanById, isValidPlan, APP_URLS } from '@/lib/stripe/config';

export async function POST(request: Request) {
  try {
    const { planId, email, customerName } = await request.json();

    // Validaciones
    if (!planId || !email) {
      return NextResponse.json({
        error: 'Plan ID y email son requeridos'
      }, { status: 400 });
    }

    if (!isValidPlan(planId)) {
      return NextResponse.json({
        error: 'Plan ID no válido'
      }, { status: 400 });
    }

    const plan = getPlanById(planId);
    if (!plan) {
      return NextResponse.json({
        error: 'Plan no encontrado'
      }, { status: 404 });
    }

    // El plan gratuito no requiere pago
    if (planId === 'free') {
      return NextResponse.json({
        error: 'El plan gratuito no requiere pago'
      }, { status: 400 });
    }

    console.log('Creating checkout session for:', { planId, email, customerName });

    // Crear o obtener el precio recurrente para el plan
    let priceId = plan.stripePriceId;

    if (!priceId) {
      // Crear precio recurrente dinámicamente
      const price = await stripe.prices.create({
        product: plan.stripeProductId,
        unit_amount: plan.price,
        currency: 'eur',
        recurring: {
          interval: 'month',
        },
        metadata: {
          planId: planId,
        },
      });
      priceId = price.id;
      console.log('Created new recurring price:', priceId);
    }

    // Crear sesión de checkout
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'], // Solo tarjetas por ahora (PayPal requiere configuración adicional)
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription', // Suscripción recurrente
      customer_email: email,
      client_reference_id: planId, // Para identificar el plan en el webhook
      metadata: {
        planId: planId,
        customerEmail: email,
        customerName: customerName || '',
      },
      subscription_data: {
        metadata: {
          planId: planId,
          customerEmail: email,
        },
      },
      success_url: `${APP_URLS.success}?session_id={CHECKOUT_SESSION_ID}&plan=${planId}`,
      cancel_url: `${APP_URLS.cancel}?plan=${planId}`,
      automatic_tax: {
        enabled: true, // Habilitar cálculo automático de impuestos
      },
      billing_address_collection: 'required', // Requerir dirección de facturación
      allow_promotion_codes: true, // Permitir códigos promocionales
    });

    console.log('Checkout session created:', session.id);

    return NextResponse.json({
      sessionId: session.id,
      url: session.url
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    
    let errorMessage = 'Error al crear la sesión de pago';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage
    }, { status: 500 });
  }
}
