'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

export default function AppRedirect() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  // Redirigir al dashboard cuando el usuario esté autenticado
  useEffect(() => {
    if (!isLoading && user) {
      router.replace('/dashboard');
    }
  }, [user, isLoading, router]);

  // Mostrar pantalla de carga mientras se redirige
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Redirigiendo al dashboard...</p>
      </div>
    </div>
  );
}
