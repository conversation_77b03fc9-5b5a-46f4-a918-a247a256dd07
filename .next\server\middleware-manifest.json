{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "26RI1+V1sdAqKAGsE/bxmDgjE8/qvZhwKRG7vEPUZv0="}}}, "functions": {}, "sortedMiddleware": ["/"]}