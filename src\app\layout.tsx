// src/app/layout.tsx
import type { Metadata } from 'next';
import './globals.css';
import RootClientLayout from './RootClientLayout';

export const metadata: Metadata = {
  title: 'OposiAI - Tu Preparador Personal Inteligente',
  description: 'Transforma tu estudio para oposiciones con el poder de la IA. Crea planes de estudio, mapas mentales, tests y flashcards de forma automática y eficiente.',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es" className="scroll-smooth">
      <body className="font-sans">
        <RootClientLayout>
          {children}
        </RootClientLayout>
      </body>
    </html>
  );
}
