// src/app/(marketing)/thank-you/page.tsx
'use client';

import React, { Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FiCheckCircle, FiHome, FiLogIn } from 'react-icons/fi';

const planDetails: { [key: string]: { name: string } } = {
  free: { name: 'Plan Gratis' },
  usuario: { name: 'Plan Usuario' },
  pro: { name: 'Plan Pro' },
};

function ThankYouContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const planId = searchParams.get('plan');
  const planName = planId && planDetails[planId] ? planDetails[planId].name : null;

  return (
    <div className="container mx-auto px-6 py-20 text-center">
      <FiCheckCircle className="text-7xl text-green-500 mx-auto mb-6" />
      <h1 className="text-4xl font-bold text-gray-800 mb-4">
        ¡Gracias por tu interés en OposiAI!
      </h1>
      {planName && (
        <p className="text-xl text-gray-700 mb-2">
          Has mostrado interés en el <span className="font-semibold text-blue-600">{planName}</span>.
        </p>
      )}
      <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
        Hemos recibido tu información correctamente. En breve, recibirás un correo electrónico en la dirección que proporcionaste con los siguientes pasos para configurar y activar tu cuenta. Por favor, revisa tu bandeja de entrada (y la carpeta de spam, por si acaso).
      </p>
      <div className="space-y-4 sm:space-y-0 sm:space-x-4">
        <button
          onClick={() => router.push('/landing')}
          className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200"
        >
          <FiHome className="mr-2" /> Volver a la página principal
        </button>
        <button
          onClick={() => router.push('/login')}
          className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 bg-gray-200 text-gray-700 font-semibold rounded-lg hover:bg-gray-300 transition-colors duration-200"
        >
          <FiLogIn className="mr-2" /> Ir a Iniciar Sesión
        </button>
      </div>
    </div>
  );
}

export default function ThankYouPage() {
  return (
    <Suspense fallback={<div className="min-h-[60vh] flex items-center justify-center">Cargando...</div>}>
      <ThankYouContent />
    </Suspense>
  );
}
