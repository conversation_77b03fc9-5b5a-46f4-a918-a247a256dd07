// src/middleware.ts

import { createServerClient } from '@supabase/ssr';
import { NextResponse, type NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value));
          supabaseResponse = NextResponse.next({
            request,
          });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          );
        },
      },
    }
  );

  // Do not run code between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  // IMPORTANT: DO NOT REMOVE auth.getUser()
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Rutas públicas que no requieren autenticación
  const publicPaths = [
    '/',
    '/landing',
    '/payment',
    '/thank-you',
    '/login',
    '/auth',
    '/api/notify-signup',
    '/api/stripe/create-checkout-session',
    '/api/stripe/webhook'
  ];

  if (!user) {
    const isPublicPath = publicPaths.some(path =>
      request.nextUrl.pathname === path ||
      (path === '/auth' && request.nextUrl.pathname.startsWith('/auth')) ||
      (path === '/api/notify-signup' && request.nextUrl.pathname.startsWith('/api/notify-signup')) ||
      (path === '/api/stripe/create-checkout-session' && request.nextUrl.pathname.startsWith('/api/stripe/create-checkout-session')) ||
      (path === '/api/stripe/webhook' && request.nextUrl.pathname.startsWith('/api/stripe/webhook'))
    );
    if (!isPublicPath) {
      const url = request.nextUrl.clone();
      url.pathname = '/'; // Redirect to landing page (now at root)
      return NextResponse.redirect(url);
    }
  }

  if (user) {
    // Si el usuario está autenticado y está en la página landing (raíz), redirigir al dashboard
    if (request.nextUrl.pathname === '/') {
      const url = request.nextUrl.clone();
      url.pathname = '/dashboard'; // Redirect to app dashboard
      return NextResponse.redirect(url);
    }

    // Otras páginas de marketing también redirigen al dashboard
    const onOtherMarketingPages = ['/landing', '/payment', '/thank-you', '/login'].includes(request.nextUrl.pathname);
    if (onOtherMarketingPages) {
      const url = request.nextUrl.clone();
      url.pathname = '/dashboard'; // Redirect to app dashboard
      return NextResponse.redirect(url);
    }
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is.
  // If you're creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse;
}

// Configuración del matcher para definir en qué rutas se ejecutará el middleware.
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     *
     * También puedes excluir rutas API específicas aquí si prefieres no hacerlo en el código del middleware,
     * pero manejarlo en el código da más flexibilidad si algunas rutas API necesitan auth y otras no.
     * Si todas las rutas /api/* están protegidas o no, puedes gestionarlo arriba.
     *
     * La expresión regular abajo intenta cubrir los casos más comunes:
     */
    '/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)',
    // Explicación de la regex mejorada:
    // /((?!             // Inicio de grupo de no coincidencia (negative lookahead)
    // _next/static      // No coincidir con _next/static
    // |_next/image      // O no coincidir con _next/image
    // |favicon.ico     // O no coincidir con favicon.ico
    // |manifest.json   // O no coincidir con manifest.json (común para PWAs)
    // |robots.txt      // O no coincidir con robots.txt
    // |.*\\..*         // O no coincidir con cualquier cosa que contenga un punto (archivos como .png, .css, etc.)
    // ).*)             // Fin del lookahead, coincide con cualquier otra cosa
  ],
};