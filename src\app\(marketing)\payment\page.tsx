// src/app/(marketing)/payment/page.tsx
'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FiMail, FiCheckCircle, FiAlertCircle, FiLoader, FiCreditCard, FiShield } from 'react-icons/fi';
import { loadStripe } from '@stripe/stripe-js';

// Inicializar Stripe solo si la key está disponible
const stripePromise = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  ? loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)
  : null;

// Define plan details - in a real app, this might come from a shared config or API
const planDetails: { [key: string]: { name: string; price?: string; priceAmount?: number; requiresPayment?: boolean } } = {
  free: { name: '<PERSON> Gratis', requiresPayment: false },
  usuario: { name: 'Plan Usuario', price: '€9.99/mes', priceAmount: 999, requiresPayment: true },
  pro: { name: 'Plan Pro', price: '€19.99/mes', priceAmount: 1999, requiresPayment: true },
};

function PaymentFormContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const planId = searchParams.get('plan');

  const [email, setEmail] = useState('');
  const [selectedPlan, setSelectedPlan] = useState<{ name: string; price?: string } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (planId && planDetails[planId]) {
      setSelectedPlan(planDetails[planId]);
    } else if (planId) {
      setError('Plan seleccionado no válido. Por favor, vuelve a la página de inicio y selecciona un plan.');
    } else {
      // Default to free plan or show error if no plan is selected?
      // For now, let's assume a plan is always passed. If not, user might be navigated away or shown an error.
      // router.replace('/landing#plans'); // Option: redirect if no plan
      setError('No se ha seleccionado ningún plan. Por favor, vuelve a la página de inicio y selecciona un plan.');
    }
  }, [planId, router]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!email.trim()) {
      setError('Por favor, introduce tu dirección de email.');
      return;
    }
    if (!planId || !selectedPlan) {
        setError('No se ha podido determinar el plan seleccionado.');
        return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Si es plan gratuito, usar el flujo anterior
      if (planId === 'free' || !selectedPlan.requiresPayment) {
        const response = await fetch('/api/notify-signup', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, plan: planId, planName: selectedPlan.name }),
        });

        let result;
        try {
          result = await response.json();
        } catch (jsonError) {
          console.error('Error parsing JSON response:', jsonError);
          setError('Error de comunicación con el servidor. Por favor, inténtalo de nuevo.');
          return;
        }

        if (response.ok) {
          setSuccess('¡Gracias! Hemos recibido tu información. Nos pondremos en contacto contigo pronto para los siguientes pasos.');
          setEmail('');
          router.push(`/thank-you?plan=${planId}`);
        } else {
          setError(result.message || 'Ocurrió un error al procesar tu solicitud. Por favor, inténtalo de nuevo.');
        }
      } else {
        // Para planes de pago, verificar si Stripe está disponible
        if (!stripePromise) {
          setError('El sistema de pagos no está configurado. Por favor, contacta con soporte.');
          return;
        }

        // Crear sesión de Stripe Checkout
        const response = await fetch('/api/stripe/create-checkout-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            planId,
            email,
            customerName: '' // Podrías agregar un campo de nombre si quieres
          }),
        });

        let result;
        try {
          result = await response.json();
        } catch (jsonError) {
          console.error('Error parsing Stripe response:', jsonError);
          setError('Error de comunicación con el sistema de pagos. Por favor, inténtalo de nuevo.');
          return;
        }

        if (response.ok && result.url) {
          // Redirigir a Stripe Checkout
          window.location.href = result.url;
        } else {
          setError(result.error || 'Error al crear la sesión de pago. Por favor, inténtalo de nuevo.');
        }
      }
    } catch (err) {
      console.error('Payment page submission error:', err);
      setError('Ocurrió un error de conexión. Por favor, revisa tu conexión a internet e inténtalo de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!planId && !error) {
    // Still determining plan or about to show error
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <FiLoader className="animate-spin text-4xl text-blue-600" />
      </div>
    );
  }

  if (error && !success) { // Only show general error if no success message is active
      // If plan is invalid, guide user back.
      if (error.includes("plan seleccionado no válido") || error.includes("No se ha seleccionado ningún plan")) {
          return (
            <div className="container mx-auto px-6 py-12 text-center">
                <FiAlertCircle className="text-5xl text-red-500 mx-auto mb-4" />
                <h1 className="text-2xl font-semibold text-gray-800 mb-4">Error</h1>
                <p className="text-red-600 mb-6">{error}</p>
                <button
                    onClick={() => router.push('/landing#plans')}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                    Volver a la página de inicio
                </button>
            </div>
          );
      }
  }


  return (
    <div className="container mx-auto px-6 py-12 max-w-lg">
      <h1 className="text-3xl font-bold text-center text-gray-800 mb-4">
        Confirmar Suscripción
      </h1>
      {selectedPlan && (
        <div className="bg-white p-6 rounded-lg shadow-md mb-8 border border-gray-200">
          <h2 className="text-xl font-semibold text-blue-700 mb-2">Has seleccionado: {selectedPlan.name}</h2>
          {selectedPlan.price && (
            <p className="text-gray-700 text-lg mb-1">Precio: <span className="font-bold">{selectedPlan.price}</span></p>
          )}
          <p className="text-sm text-gray-600">
            {selectedPlan.requiresPayment ? (
              <>
                Completa el siguiente formulario para proceder al pago seguro.
                Aceptamos tarjetas de crédito/débito y PayPal.
              </>
            ) : (
              <>
                Completa el siguiente formulario para continuar. Recibirás un email de{' '}
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                  <EMAIL>
                </a>{' '}
                con los detalles para activar tu cuenta.
              </>
            )}
          </p>

          {selectedPlan.requiresPayment && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2 text-green-700">
                <FiShield className="w-4 h-4" />
                <span className="text-sm font-medium">Pago 100% Seguro</span>
              </div>
              <p className="text-xs text-green-600 mt-1">
                Procesado por Stripe. Tus datos están protegidos con encriptación SSL.
              </p>
            </div>
          )}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white p-8 rounded-lg shadow-md border border-gray-200">
        <div className="mb-6">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Tu Dirección de Email
          </label>
          <div className="relative">
            <span className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiMail className="text-gray-400" />
            </span>
            <input
              type="email"
              id="email"
              name="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="<EMAIL>"
              disabled={isLoading || !!success}
            />
          </div>
        </div>

        {error && !success && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 border border-red-200 rounded-md flex items-center">
            <FiAlertCircle className="mr-2 flex-shrink-0" />
            {error}
          </div>
        )}
        {success && (
          <div className="mb-4 p-3 bg-green-50 text-green-700 border border-green-200 rounded-md flex items-center">
            <FiCheckCircle className="mr-2 flex-shrink-0" />
            {success}
          </div>
        )}

        <button
          type="submit"
          disabled={isLoading || !!success || !planId}
          className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-60 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <>
              <FiLoader className="animate-spin mr-2" />
              {selectedPlan?.requiresPayment ? 'Redirigiendo a pago...' : 'Procesando...'}
            </>
          ) : success ? (
            <>
              <FiCheckCircle className="mr-2" /> Información Recibida
            </>
          ) : (
            <>
              {selectedPlan?.requiresPayment ? (
                <>
                  <FiCreditCard className="mr-2" /> Proceder al Pago Seguro
                </>
              ) : (
                'Confirmar y Enviar Datos'
              )}
            </>
          )}
        </button>
         {!success && planId && (
             <p className="text-xs text-gray-500 mt-4 text-center">
                Al hacer clic en "Confirmar y Enviar Datos", tus datos serán enviados a OposiAI para la creación de tu perfil.
                No se realizará ningún cargo en este momento.
             </p>
         )}
      </form>
    </div>
  );
}

// Use Suspense for useSearchParams hook as recommended by Next.js
export default function PaymentPage() {
  return (
    <Suspense fallback={<div className="min-h-[60vh] flex items-center justify-center"><FiLoader className="animate-spin text-4xl text-blue-600" /></div>}>
      <PaymentFormContent />
    </Suspense>
  );
}
