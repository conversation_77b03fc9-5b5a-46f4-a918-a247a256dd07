/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(marketing)/landing/page";
exports.ids = ["app/(marketing)/landing/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(marketing)%2Flanding%2Fpage&page=%2F(marketing)%2Flanding%2Fpage&appPaths=%2F(marketing)%2Flanding%2Fpage&pagePath=private-next-app-dir%2F(marketing)%2Flanding%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(marketing)%2Flanding%2Fpage&page=%2F(marketing)%2Flanding%2Fpage&appPaths=%2F(marketing)%2Flanding%2Fpage&pagePath=private-next-app-dir%2F(marketing)%2Flanding%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(marketing)/layout.tsx */ \"(rsc)/./src/app/(marketing)/layout.tsx\"));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page8 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(marketing)/landing/page.tsx */ \"(rsc)/./src/app/(marketing)/landing/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(marketing)',\n        {\n        children: [\n        'landing',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page8, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\"],\n'not-found': [module5, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module6, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module7, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(marketing)/landing/page\",\n        pathname: \"/landing\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(marketing)%2Flanding%2Fpage&page=%2F(marketing)%2Flanding%2Fpage&appPaths=%2F(marketing)%2Flanding%2Fpage&pagePath=private-next-app-dir%2F(marketing)%2Flanding%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Csrc%5C%5Capp%5C%5C(marketing)%5C%5Clanding%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Csrc%5C%5Capp%5C%5C(marketing)%5C%5Clanding%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(marketing)/landing/page.tsx */ \"(rsc)/./src/app/(marketing)/landing/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wc2lBSSUyMGNvbiUyMGxhZGluZyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1QyhtYXJrZXRpbmcpJTVDJTVDbGFuZGluZyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBK0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wc2lBSSBjb24gbGFkaW5nXFxcXHNyY1xcXFxhcHBcXFxcKG1hcmtldGluZylcXFxcbGFuZGluZ1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Csrc%5C%5Capp%5C%5C(marketing)%5C%5Clanding%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/(marketing)/landing/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/(marketing)/landing/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\augment-projects\\\\\\\\OpsiAI con lading\\\\\\\\src\\\\\\\\app\\\\\\\\(marketing)\\\\\\\\landing\\\\\\\\page.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(marketing)/landing/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/(marketing)/layout.tsx":
/*!****************************************!*\
  !*** ./src/app/(marketing)/layout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarketingLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _barrel_optimize_names_FiExternalLink_FiLogIn_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiExternalLink,FiLogIn!=!react-icons/fi */ \"(rsc)/./node_modules/react-icons/fi/index.mjs\");\n// src/app/(marketing)/layout.tsx\n\n // Ensure Tailwind styles are applied\n // For icons\nfunction MarketingLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        className: \"scroll-smooth\",\n        children: [\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"font-sans bg-gray-100 text-gray-900 flex flex-col min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white shadow-md sticky top-0 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/landing\",\n                                    className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:opacity-80 transition-opacity\",\n                                    children: \"OposiAI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/login\",\n                                        className: \"inline-flex items-center px-5 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiLogIn_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiLogIn, {\n                                                className: \"mr-2 -ml-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Iniciar Sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-gray-800 text-gray-300 border-t border-gray-700 mt-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-xl font-bold text-white mb-3\",\n                                                    children: \"OposiAI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: \"Tu asistente inteligente para la preparaci\\xf3n de oposiciones. Optimizamos tu estudio con IA.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-lg font-semibold text-white mb-3\",\n                                                    children: \"Enlaces R\\xe1pidos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/landing#plans\",\n                                                                className: \"hover:text-blue-400 transition-colors\",\n                                                                children: \"Planes\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                                lineNumber: 46,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                            lineNumber: 46,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"/landing#how-it-works\",\n                                                                className: \"hover:text-blue-400 transition-colors\",\n                                                                children: \"C\\xf3mo Funciona\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                                lineNumber: 47,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                            lineNumber: 47,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"text-lg font-semibold text-white mb-3\",\n                                                    children: \"Legal\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-blue-400 transition-colors\",\n                                                                children: \"T\\xe9rminos de Servicio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                                lineNumber: 54,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                            lineNumber: 54,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"hover:text-blue-400 transition-colors\",\n                                                                children: \"Pol\\xedtica de Privacidad\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                                lineNumber: 55,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                            lineNumber: 55,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"mailto:<EMAIL>\",\n                                                                className: \"hover:text-blue-400 transition-colors flex items-center\",\n                                                                children: [\n                                                                    \"Contacto \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiLogIn_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiExternalLink, {\n                                                                        className: \"ml-1.5 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                                        lineNumber: 57,\n                                                                        columnNumber: 30\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                                lineNumber: 56,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                            lineNumber: 56,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-700 pt-8 text-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"\\xa9 \",\n                                                new Date().getFullYear(),\n                                                \" OposiAI. Todos los derechos reservados.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1\",\n                                            children: \"Una herramienta para opositores, por opositores.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(marketing)/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b63ad14717ba\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wc2lBSSBjb24gbGFkaW5nXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJiNjNhZDE0NzE3YmFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n// src/app/layout.tsx\n// This is the new root layout.\n// Route group layouts will provide the main html/body structure.\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEscUJBQXFCO0FBQ3JCLCtCQUErQjtBQUMvQixpRUFBaUU7O0FBQ2xELFNBQVNBLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUFPO2tCQUFHQTs7QUFDWiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcHNpQUkgY29uIGxhZGluZ1xcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2FwcC9sYXlvdXQudHN4XG4vLyBUaGlzIGlzIHRoZSBuZXcgcm9vdCBsYXlvdXQuXG4vLyBSb3V0ZSBncm91cCBsYXlvdXRzIHdpbGwgcHJvdmlkZSB0aGUgbWFpbiBodG1sL2JvZHkgc3RydWN0dXJlLlxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+O1xufVxuIl0sIm5hbWVzIjpbIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wc2lBSSUyMGNvbiUyMGxhZGluZyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wc2lBSSUyMGNvbiUyMGxhZGluZyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wc2lBSSUyMGNvbiUyMGxhZGluZyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wc2lBSSUyMGNvbiUyMGxhZGluZyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3BzaUFJJTIwY29uJTIwbGFkaW5nJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNuYWF0YSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNPcHNpQUklMjBjb24lMjBsYWRpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wc2lBSSUyMGNvbiUyMGxhZGluZyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3BzaUFJJTIwY29uJTIwbGFkaW5nJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQW1LO0FBQ25LO0FBQ0EsME9BQXNLO0FBQ3RLO0FBQ0EsME9BQXNLO0FBQ3RLO0FBQ0Esb1JBQTRMO0FBQzVMO0FBQ0Esd09BQXFLO0FBQ3JLO0FBQ0EsNFBBQWdMO0FBQ2hMO0FBQ0Esa1FBQW1MO0FBQ25MO0FBQ0Esc1FBQW9MIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcHNpQUkgY29uIGxhZGluZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcHNpQUkgY29uIGxhZGluZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcHNpQUkgY29uIGxhZGluZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcHNpQUkgY29uIGxhZGluZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcHNpQUkgY29uIGxhZGluZ1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wc2lBSSBjb24gbGFkaW5nXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wc2lBSSBjb24gbGFkaW5nXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wc2lBSSBjb24gbGFkaW5nXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Csrc%5C%5Capp%5C%5C(marketing)%5C%5Clanding%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Csrc%5C%5Capp%5C%5C(marketing)%5C%5Clanding%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(marketing)/landing/page.tsx */ \"(ssr)/./src/app/(marketing)/landing/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wc2lBSSUyMGNvbiUyMGxhZGluZyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1QyhtYXJrZXRpbmcpJTVDJTVDbGFuZGluZyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBK0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG5hYXRhXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXE9wc2lBSSBjb24gbGFkaW5nXFxcXHNyY1xcXFxhcHBcXFxcKG1hcmtldGluZylcXFxcbGFuZGluZ1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Csrc%5C%5Capp%5C%5C(marketing)%5C%5Clanding%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COpsiAI%20con%20lading%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/(marketing)/landing/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/(marketing)/landing/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiArrowRight_FiBox_FiCheckCircle_FiPlayCircle_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FiArrowRight,FiBox,FiCheckCircle,FiPlayCircle,FiUsers,FiZap!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n// src/app/(marketing)/landing/page.tsx\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n // Example icons\n\n// Placeholder component for video sections\nconst VideoPlaceholder = ({ title })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-200 aspect-video w-full max-w-2xl mx-auto rounded-lg flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBox_FiCheckCircle_FiPlayCircle_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiPlayCircle, {\n                    className: \"text-5xl text-gray-500 mx-auto mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\nconst PlanCard = ({ title, price, features, ctaText, planId, bgColorClass, textColorClass, borderColorClass, router })=>{\n    const handleSelectPlan = ()=>{\n        router.push(`/payment?plan=${planId}`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `border ${borderColorClass} rounded-xl shadow-lg p-8 flex flex-col h-full ${bgColorClass}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: `text-2xl font-bold mb-4 ${textColorClass}`,\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: `text-3xl font-extrabold mb-6 ${textColorClass}`,\n                children: price\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"space-y-3 mb-8 flex-grow\",\n                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: `flex items-start ${textColorClass}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBox_FiCheckCircle_FiPlayCircle_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiCheckCircle, {\n                                className: `w-5 h-5 mr-3 mt-1 flex-shrink-0 ${textColorClass === 'text-white' ? 'text-green-300' : 'text-green-500'}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: feature\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleSelectPlan,\n                className: `w-full py-3 px-6 rounded-lg font-semibold transition-transform duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 ${textColorClass === 'text-white' ? `bg-white ${bgColorClass.includes('purple') ? 'text-purple-700' : 'text-blue-700'} hover:bg-gray-100 focus:ring-white` : `${bgColorClass.includes('purple') ? 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500' : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'} text-white`}`,\n                children: [\n                    ctaText,\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBox_FiCheckCircle_FiPlayCircle_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiArrowRight, {\n                        className: \"inline ml-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 19\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\nfunction LandingPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const plans = [\n        {\n            title: 'Free',\n            price: 'Gratis',\n            features: [\n                'Sube 1 documento',\n                '5 mapas mentales a la semana',\n                '30 preguntas tipo test a la semana',\n                '30 flashcards a la semana'\n            ],\n            ctaText: 'Empezar Gratis',\n            planId: 'free',\n            bgColorClass: 'bg-white',\n            textColorClass: 'text-gray-800',\n            borderColorClass: 'border-gray-300'\n        },\n        {\n            title: 'Usuario',\n            price: '€X.XX/mes',\n            features: [\n                'Sube 5 documentos',\n                '10 mapas mentales a la semana',\n                '150 preguntas tipo test a la semana',\n                '150 flashcards a la semana'\n            ],\n            ctaText: 'Seleccionar Plan Usuario',\n            planId: 'usuario',\n            bgColorClass: 'bg-gradient-to-br from-blue-600 to-indigo-700',\n            textColorClass: 'text-white',\n            borderColorClass: 'border-blue-700'\n        },\n        {\n            title: 'Pro',\n            price: '€Y.YY/mes',\n            features: [\n                'Sube un temario completo',\n                'Planificación de estudio hasta examen mediante IA',\n                'Mapas mentales ilimitados',\n                'Preguntas tipo test ilimitadas',\n                'Flashcards ilimitadas'\n            ],\n            ctaText: 'Seleccionar Plan Pro',\n            planId: 'pro',\n            bgColorClass: 'bg-gradient-to-br from-purple-600 to-violet-700',\n            textColorClass: 'text-white',\n            borderColorClass: 'border-purple-700'\n        }\n    ];\n    const howItWorksSteps = [\n        {\n            title: '1. Elige tu Temario',\n            description: 'Selecciona un temario predeterminado de nuestra base de datos o sube manualmente los documentos de tu oposición.',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBox_FiCheckCircle_FiPlayCircle_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiBox, {\n                className: \"w-12 h-12 text-blue-600 mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '2. Selecciona el Tema',\n            description: 'Escoge el tema específico que quieres preparar y enfoca tu estudio de manera eficiente.',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBox_FiCheckCircle_FiPlayCircle_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiCheckCircle, {\n                className: \"w-12 h-12 text-blue-600 mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '3. Planifica tu Estudio (Modo Guiado)',\n            description: 'Responde a preguntas predeterminadas para ayudarnos a entender tus necesidades y crear un borrador de tu plan.',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBox_FiCheckCircle_FiPlayCircle_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiUsers, {\n                className: \"w-12 h-12 text-blue-600 mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '4. Planificación IA (Modo Pro)',\n            description: 'Deja que nuestra Inteligencia Artificial diseñe un plan de estudio optimizado y personalizado para ti.',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBox_FiCheckCircle_FiPlayCircle_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiZap, {\n                className: \"w-12 h-12 text-blue-600 mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '5. Empieza tu Preparación Inteligente',\n            description: 'OposiAI, tu preparador personal, te ayuda a crear mapas mentales espectaculares, flashcards (con curva del olvido), tests, y resuelve tus dudas.',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiArrowRight_FiBox_FiCheckCircle_FiPlayCircle_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiPlayCircle, {\n                className: \"w-12 h-12 text-blue-600 mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gradient-to-r from-blue-600 to-purple-700 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-extrabold mb-6\",\n                            children: \"OposiAI: Tu Preparador Personal Inteligente\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl mb-10 max-w-3xl mx-auto\",\n                            children: \"Transforma tu estudio para oposiciones con el poder de la IA. Crea planes de estudio, mapas mentales, tests y flashcards de forma autom\\xe1tica y eficiente.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('#plans'),\n                            className: \"bg-white text-blue-700 font-bold py-3 px-8 rounded-lg text-lg hover:bg-gray-100 transition-colors duration-300 shadow-lg\",\n                            children: \"Ver Planes de Suscripci\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"plans\",\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-center text-gray-800 mb-4\",\n                            children: \"Elige el Plan Perfecto para Ti\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-gray-600 mb-16 max-w-2xl mx-auto\",\n                            children: \"Tenemos opciones para cada tipo de opositor. Comienza gratis o desbloquea todo el potencial de OposiAI con nuestros planes avanzados.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlanCard, {\n                                    ...plan,\n                                    router: router\n                                }, plan.planId, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-center text-gray-800 mb-16\",\n                            children: \"\\xbfC\\xf3mo Funciona OposiAI?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-1 gap-10 mb-12\",\n                            children: howItWorksSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-8 rounded-xl shadow-lg flex items-start space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: step.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-semibold text-gray-800 mb-3\",\n                                                    children: step.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 leading-relaxed\",\n                                                    children: step.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-semibold text-gray-800 mb-6\",\n                                    children: \"Videos Explicativos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 14\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                    children: \"Mira c\\xf3mo cada fase de OposiAI te ayuda a optimizar tu preparaci\\xf3n.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 14\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoPlaceholder, {\n                                            title: \"Video: Eligiendo y Configurando tu Temario\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoPlaceholder, {\n                                            title: \"Video: Planificaci\\xf3n de Estudio Inteligente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoPlaceholder, {\n                                            title: \"Video: Herramientas de Preparaci\\xf3n IA (Mapas, Tests, Flashcards)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoPlaceholder, {\n                                            title: \"Video: Tu Preparador Personal OposAI en Acci\\xf3n\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 14\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gradient-to-r from-blue-500 to-purple-600 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-6\",\n                            children: \"\\xbfListo para Empezar?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg mb-8 max-w-xl mx-auto\",\n                            children: \"\\xdanete a OposiAI y lleva tu preparaci\\xf3n al siguiente nivel.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/login'),\n                            className: \"bg-white text-blue-700 font-bold py-3 px-10 rounded-lg text-lg hover:bg-gray-100 transition-colors duration-300 shadow-lg\",\n                            children: \"Iniciar Sesi\\xf3n\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\(marketing)\\\\landing\\\\page.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(marketing)/landing/page.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-icons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(marketing)%2Flanding%2Fpage&page=%2F(marketing)%2Flanding%2Fpage&appPaths=%2F(marketing)%2Flanding%2Fpage&pagePath=private-next-app-dir%2F(marketing)%2Flanding%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();