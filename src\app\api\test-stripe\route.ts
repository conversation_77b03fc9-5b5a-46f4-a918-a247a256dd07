// src/app/api/test-stripe/route.ts
import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Stripe API test endpoint working',
    timestamp: new Date().toISOString()
  });
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    return NextResponse.json({
      message: 'Stripe API test POST working',
      receivedData: body,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      error: 'Error parsing request',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 400 });
  }
}
