// src/app/api/test-stripe/route.ts
import { NextResponse } from 'next/server';
import Stripe from 'stripe';

export async function GET() {
  try {
    console.log('Testing Stripe connection...');

    // Verificar que tenemos la clave de Stripe
    const stripeKey = process.env.STRIPE_SECRET_KEY;
    if (!stripeKey) {
      return NextResponse.json({
        error: 'STRIPE_SECRET_KEY not found in environment variables'
      }, { status: 500 });
    }

    console.log('Stripe key found, length:', stripeKey.length);
    console.log('Stripe key starts with:', stripeKey.substring(0, 20) + '...');

    // Intentar inicializar Stripe
    const stripe = new Stripe(stripeKey, {
      apiVersion: '2024-12-18.acacia',
    });

    // Hacer una llamada simple a la API de Stripe
    const account = await stripe.accounts.retrieve();

    return NextResponse.json({
      message: 'Stripe API connection successful',
      accountId: account.id,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Stripe test error:', error);

    return NextResponse.json({
      error: 'Stripe connection failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    return NextResponse.json({
      message: 'Stripe API test POST working',
      receivedData: body,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      error: 'Error parsing request',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 400 });
  }
}
