/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/notify-signup/route";
exports.ids = ["app/api/notify-signup/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotify-signup%2Froute&page=%2Fapi%2Fnotify-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotify-signup%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotify-signup%2Froute&page=%2Fapi%2Fnotify-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotify-signup%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OpsiAI_con_lading_src_app_api_notify_signup_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/notify-signup/route.ts */ \"(rsc)/./src/app/api/notify-signup/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/notify-signup/route\",\n        pathname: \"/api/notify-signup\",\n        filename: \"route\",\n        bundlePath: \"app/api/notify-signup/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OpsiAI con lading\\\\src\\\\app\\\\api\\\\notify-signup\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OpsiAI_con_lading_src_app_api_notify_signup_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotify-signup%2Froute&page=%2Fapi%2Fnotify-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotify-signup%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/notify-signup/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/notify-signup/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n// src/app/api/notify-signup/route.ts\n\n\nasync function POST(request) {\n    try {\n        const { email, plan, planName } = await request.json();\n        if (!email || !plan || !planName) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Faltan datos: email, plan y planName son requeridos.'\n            }, {\n                status: 400\n            });\n        }\n        // Create a test account for Ethereal\n        // In a real app, use process.env for SMTP config\n        let testAccount = await nodemailer__WEBPACK_IMPORTED_MODULE_1__.createTestAccount();\n        // Log Ethereal credentials to the console when the subtask runs.\n        // This is important for the main agent to be able to report them.\n        console.log('ETHEREAL_USER:', testAccount.user);\n        console.log('ETHEREAL_PASS:', testAccount.pass);\n        const transporter = nodemailer__WEBPACK_IMPORTED_MODULE_1__.createTransport({\n            host: 'smtp.ethereal.email',\n            port: 587,\n            secure: false,\n            auth: {\n                user: testAccount.user,\n                pass: testAccount.pass\n            }\n        });\n        const mailOptions = {\n            from: `\"OposiAI Notificaciones\" <${testAccount.user}>`,\n            to: '<EMAIL>',\n            subject: `Nueva Solicitud de Suscripción OposiAI: Plan ${planName}`,\n            text: `Un usuario ha solicitado una suscripción:\n\nEmail: ${email}\nPlan ID: ${plan}\nPlan Nombre: ${planName}\n\nPor favor, procede a crear su perfil.`,\n            html: `<p>Un usuario ha solicitado una suscripción:</p>\n             <ul>\n               <li><strong>Email:</strong> ${email}</li>\n               <li><strong>Plan ID:</strong> ${plan}</li>\n               <li><strong>Plan Nombre:</strong> ${planName}</li>\n             </ul>\n             <p>Por favor, procede a crear su perfil.</p>`\n        };\n        let info = await transporter.sendMail(mailOptions);\n        // Log Ethereal message preview URL to the console.\n        const previewUrl = nodemailer__WEBPACK_IMPORTED_MODULE_1__.getTestMessageUrl(info);\n        console.log('ETHEREAL_PREVIEW_URL:', previewUrl);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Solicitud recibida. Se ha enviado la notificación.',\n            etherealPreviewUrl: previewUrl // Also return it in response for easier testing\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error('Error en /api/notify-signup:', error);\n        // Check if error is a known type, e.g. from nodemailer\n        let errorMessage = 'Error al procesar la solicitud.';\n        if (error instanceof Error) {\n            errorMessage = error.message;\n        }\n        // Log the specific error message that will be returned\n        console.error('Returning error message:', errorMessage);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9ub3RpZnktc2lnbnVwL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLHFDQUFxQztBQUNNO0FBQ1A7QUFFN0IsZUFBZUUsS0FBS0MsT0FBZ0I7SUFDekMsSUFBSTtRQUNGLE1BQU0sRUFBRUMsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLFFBQVEsRUFBRSxHQUFHLE1BQU1ILFFBQVFJLElBQUk7UUFFcEQsSUFBSSxDQUFDSCxTQUFTLENBQUNDLFFBQVEsQ0FBQ0MsVUFBVTtZQUNoQyxPQUFPTixxREFBWUEsQ0FBQ08sSUFBSSxDQUFDO2dCQUFFQyxTQUFTO1lBQXVELEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUM5RztRQUVBLHFDQUFxQztRQUNyQyxpREFBaUQ7UUFDakQsSUFBSUMsY0FBYyxNQUFNVCx5REFBNEI7UUFDcEQsaUVBQWlFO1FBQ2pFLGtFQUFrRTtRQUNsRVcsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQkgsWUFBWUksSUFBSTtRQUM5Q0YsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQkgsWUFBWUssSUFBSTtRQUc5QyxNQUFNQyxjQUFjZix1REFBMEIsQ0FBQztZQUM3Q2lCLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLE1BQU07Z0JBQ0pQLE1BQU1KLFlBQVlJLElBQUk7Z0JBQ3RCQyxNQUFNTCxZQUFZSyxJQUFJO1lBQ3hCO1FBQ0Y7UUFFQSxNQUFNTyxjQUFjO1lBQ2xCQyxNQUFNLENBQUMsMEJBQTBCLEVBQUViLFlBQVlJLElBQUksQ0FBQyxDQUFDLENBQUM7WUFDdERVLElBQUk7WUFDSkMsU0FBUyxDQUFDLDZDQUE2QyxFQUFFbkIsVUFBVTtZQUNuRW9CLE1BQU0sQ0FBQzs7T0FFTixFQUFFdEIsTUFBTTtTQUNOLEVBQUVDLEtBQUs7YUFDSCxFQUFFQyxTQUFTOztxQ0FFYSxDQUFDO1lBQ2hDcUIsTUFBTSxDQUFDOzsyQ0FFOEIsRUFBRXZCLE1BQU07NkNBQ04sRUFBRUMsS0FBSztpREFDSCxFQUFFQyxTQUFTOzt5REFFSCxDQUFDO1FBQ3REO1FBRUEsSUFBSXNCLE9BQU8sTUFBTVosWUFBWWEsUUFBUSxDQUFDUDtRQUV0QyxtREFBbUQ7UUFDbkQsTUFBTVEsYUFBYTdCLHlEQUE0QixDQUFDMkI7UUFDaERoQixRQUFRQyxHQUFHLENBQUMseUJBQXlCaUI7UUFFckMsT0FBTzlCLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7WUFDckJDLFNBQVM7WUFDVHdCLG9CQUFvQkYsV0FBVyxnREFBZ0Q7UUFDbkYsR0FBRztZQUFFckIsUUFBUTtRQUFJO0lBRW5CLEVBQUUsT0FBT3dCLE9BQU87UUFDZHJCLFFBQVFxQixLQUFLLENBQUMsZ0NBQWdDQTtRQUM5Qyx1REFBdUQ7UUFDdkQsSUFBSUMsZUFBZTtRQUNuQixJQUFJRCxpQkFBaUJFLE9BQU87WUFDeEJELGVBQWVELE1BQU16QixPQUFPO1FBQ2hDO1FBQ0EsdURBQXVEO1FBQ3ZESSxRQUFRcUIsS0FBSyxDQUFDLDRCQUE0QkM7UUFDMUMsT0FBT2xDLHFEQUFZQSxDQUFDTyxJQUFJLENBQUM7WUFBRUMsU0FBUzBCO1FBQWEsR0FBRztZQUFFekIsUUFBUTtRQUFJO0lBQ3BFO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3BzaUFJIGNvbiBsYWRpbmdcXHNyY1xcYXBwXFxhcGlcXG5vdGlmeS1zaWdudXBcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9hcHAvYXBpL25vdGlmeS1zaWdudXAvcm91dGUudHNcbmltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCBub2RlbWFpbGVyIGZyb20gJ25vZGVtYWlsZXInO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBlbWFpbCwgcGxhbiwgcGxhbk5hbWUgfSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xuXG4gICAgaWYgKCFlbWFpbCB8fCAhcGxhbiB8fCAhcGxhbk5hbWUpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IG1lc3NhZ2U6ICdGYWx0YW4gZGF0b3M6IGVtYWlsLCBwbGFuIHkgcGxhbk5hbWUgc29uIHJlcXVlcmlkb3MuJyB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgIH1cblxuICAgIC8vIENyZWF0ZSBhIHRlc3QgYWNjb3VudCBmb3IgRXRoZXJlYWxcbiAgICAvLyBJbiBhIHJlYWwgYXBwLCB1c2UgcHJvY2Vzcy5lbnYgZm9yIFNNVFAgY29uZmlnXG4gICAgbGV0IHRlc3RBY2NvdW50ID0gYXdhaXQgbm9kZW1haWxlci5jcmVhdGVUZXN0QWNjb3VudCgpO1xuICAgIC8vIExvZyBFdGhlcmVhbCBjcmVkZW50aWFscyB0byB0aGUgY29uc29sZSB3aGVuIHRoZSBzdWJ0YXNrIHJ1bnMuXG4gICAgLy8gVGhpcyBpcyBpbXBvcnRhbnQgZm9yIHRoZSBtYWluIGFnZW50IHRvIGJlIGFibGUgdG8gcmVwb3J0IHRoZW0uXG4gICAgY29uc29sZS5sb2coJ0VUSEVSRUFMX1VTRVI6JywgdGVzdEFjY291bnQudXNlcik7XG4gICAgY29uc29sZS5sb2coJ0VUSEVSRUFMX1BBU1M6JywgdGVzdEFjY291bnQucGFzcyk7XG5cblxuICAgIGNvbnN0IHRyYW5zcG9ydGVyID0gbm9kZW1haWxlci5jcmVhdGVUcmFuc3BvcnQoe1xuICAgICAgaG9zdDogJ3NtdHAuZXRoZXJlYWwuZW1haWwnLCAvLyBFdGhlcmVhbCBTTVRQXG4gICAgICBwb3J0OiA1ODcsXG4gICAgICBzZWN1cmU6IGZhbHNlLCAvLyB0cnVlIGZvciA0NjUsIGZhbHNlIGZvciBvdGhlciBwb3J0c1xuICAgICAgYXV0aDoge1xuICAgICAgICB1c2VyOiB0ZXN0QWNjb3VudC51c2VyLCAvLyBHZW5lcmF0ZWQgRXRoZXJlYWwgdXNlclxuICAgICAgICBwYXNzOiB0ZXN0QWNjb3VudC5wYXNzLCAvLyBHZW5lcmF0ZWQgRXRoZXJlYWwgcGFzc3dvcmRcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICBjb25zdCBtYWlsT3B0aW9ucyA9IHtcbiAgICAgIGZyb206IGBcIk9wb3NpQUkgTm90aWZpY2FjaW9uZXNcIiA8JHt0ZXN0QWNjb3VudC51c2VyfT5gLCAvLyBzZW5kZXIgYWRkcmVzc1xuICAgICAgdG86ICdqdWdvbXJ1c0BnbWFpbC5jb20nLCAvLyBsaXN0IG9mIHJlY2VpdmVyc1xuICAgICAgc3ViamVjdDogYE51ZXZhIFNvbGljaXR1ZCBkZSBTdXNjcmlwY2nDs24gT3Bvc2lBSTogUGxhbiAke3BsYW5OYW1lfWAsIC8vIFN1YmplY3QgbGluZVxuICAgICAgdGV4dDogYFVuIHVzdWFyaW8gaGEgc29saWNpdGFkbyB1bmEgc3VzY3JpcGNpw7NuOlxuXG5FbWFpbDogJHtlbWFpbH1cblBsYW4gSUQ6ICR7cGxhbn1cblBsYW4gTm9tYnJlOiAke3BsYW5OYW1lfVxuXG5Qb3IgZmF2b3IsIHByb2NlZGUgYSBjcmVhciBzdSBwZXJmaWwuYCwgLy8gcGxhaW4gdGV4dCBib2R5XG4gICAgICBodG1sOiBgPHA+VW4gdXN1YXJpbyBoYSBzb2xpY2l0YWRvIHVuYSBzdXNjcmlwY2nDs246PC9wPlxuICAgICAgICAgICAgIDx1bD5cbiAgICAgICAgICAgICAgIDxsaT48c3Ryb25nPkVtYWlsOjwvc3Ryb25nPiAke2VtYWlsfTwvbGk+XG4gICAgICAgICAgICAgICA8bGk+PHN0cm9uZz5QbGFuIElEOjwvc3Ryb25nPiAke3BsYW59PC9saT5cbiAgICAgICAgICAgICAgIDxsaT48c3Ryb25nPlBsYW4gTm9tYnJlOjwvc3Ryb25nPiAke3BsYW5OYW1lfTwvbGk+XG4gICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICA8cD5Qb3IgZmF2b3IsIHByb2NlZGUgYSBjcmVhciBzdSBwZXJmaWwuPC9wPmAsIC8vIGh0bWwgYm9keVxuICAgIH07XG5cbiAgICBsZXQgaW5mbyA9IGF3YWl0IHRyYW5zcG9ydGVyLnNlbmRNYWlsKG1haWxPcHRpb25zKTtcblxuICAgIC8vIExvZyBFdGhlcmVhbCBtZXNzYWdlIHByZXZpZXcgVVJMIHRvIHRoZSBjb25zb2xlLlxuICAgIGNvbnN0IHByZXZpZXdVcmwgPSBub2RlbWFpbGVyLmdldFRlc3RNZXNzYWdlVXJsKGluZm8pO1xuICAgIGNvbnNvbGUubG9nKCdFVEhFUkVBTF9QUkVWSUVXX1VSTDonLCBwcmV2aWV3VXJsKTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIG1lc3NhZ2U6ICdTb2xpY2l0dWQgcmVjaWJpZGEuIFNlIGhhIGVudmlhZG8gbGEgbm90aWZpY2FjacOzbi4nLFxuICAgICAgICBldGhlcmVhbFByZXZpZXdVcmw6IHByZXZpZXdVcmwgLy8gQWxzbyByZXR1cm4gaXQgaW4gcmVzcG9uc2UgZm9yIGVhc2llciB0ZXN0aW5nXG4gICAgfSwgeyBzdGF0dXM6IDIwMCB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGVuIC9hcGkvbm90aWZ5LXNpZ251cDonLCBlcnJvcik7XG4gICAgLy8gQ2hlY2sgaWYgZXJyb3IgaXMgYSBrbm93biB0eXBlLCBlLmcuIGZyb20gbm9kZW1haWxlclxuICAgIGxldCBlcnJvck1lc3NhZ2UgPSAnRXJyb3IgYWwgcHJvY2VzYXIgbGEgc29saWNpdHVkLic7XG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3IubWVzc2FnZTtcbiAgICB9XG4gICAgLy8gTG9nIHRoZSBzcGVjaWZpYyBlcnJvciBtZXNzYWdlIHRoYXQgd2lsbCBiZSByZXR1cm5lZFxuICAgIGNvbnNvbGUuZXJyb3IoJ1JldHVybmluZyBlcnJvciBtZXNzYWdlOicsIGVycm9yTWVzc2FnZSk7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgbWVzc2FnZTogZXJyb3JNZXNzYWdlIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJub2RlbWFpbGVyIiwiUE9TVCIsInJlcXVlc3QiLCJlbWFpbCIsInBsYW4iLCJwbGFuTmFtZSIsImpzb24iLCJtZXNzYWdlIiwic3RhdHVzIiwidGVzdEFjY291bnQiLCJjcmVhdGVUZXN0QWNjb3VudCIsImNvbnNvbGUiLCJsb2ciLCJ1c2VyIiwicGFzcyIsInRyYW5zcG9ydGVyIiwiY3JlYXRlVHJhbnNwb3J0IiwiaG9zdCIsInBvcnQiLCJzZWN1cmUiLCJhdXRoIiwibWFpbE9wdGlvbnMiLCJmcm9tIiwidG8iLCJzdWJqZWN0IiwidGV4dCIsImh0bWwiLCJpbmZvIiwic2VuZE1haWwiLCJwcmV2aWV3VXJsIiwiZ2V0VGVzdE1lc3NhZ2VVcmwiLCJldGhlcmVhbFByZXZpZXdVcmwiLCJlcnJvciIsImVycm9yTWVzc2FnZSIsIkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/notify-signup/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/nodemailer"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotify-signup%2Froute&page=%2Fapi%2Fnotify-signup%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotify-signup%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COpsiAI%20con%20lading&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();