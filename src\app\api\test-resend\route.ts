// src/app/api/test-resend/route.ts
import { NextResponse } from 'next/server';
import { Resend } from 'resend';

export async function GET() {
  try {
    if (!process.env.RESEND_API_KEY) {
      return NextResponse.json({
        error: 'RESEND_API_KEY no está configurada'
      }, { status: 500 });
    }

    const resend = new Resend(process.env.RESEND_API_KEY);
    
    // Intentar enviar un email de prueba
    const { data, error } = await resend.emails.send({
      from: 'OposiAI Test <<EMAIL>>',
      to: ['<EMAIL>'], // Email de prueba de Resend
      subject: 'Test de Configuración OposiAI',
      html: '<p>Este es un email de prueba para verificar la configuración de Resend.</p>',
      text: 'Este es un email de prueba para verificar la configuración de Resend.',
    });

    if (error) {
      console.error('Error en test de Resend:', error);
      return NextResponse.json({
        success: false,
        error: error.message,
        details: error
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: 'Resend configurado correctamente',
      emailId: data?.id,
      data: data
    });

  } catch (error) {
    console.error('Error en test de Resend:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}
