'use client';

import React from 'react';
import { AuthProvider } from '@/contexts/AuthContext';
import { BackgroundTasksProvider } from '@/contexts/BackgroundTasksContext';
import { Toaster } from 'react-hot-toast';

interface RootClientLayoutProps {
  children: React.ReactNode;
}

export default function RootClientLayout({ children }: RootClientLayoutProps) {
  return (
    <BackgroundTasksProvider>
      <AuthProvider>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 5000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              style: {
                background: '#10b981',
                color: '#fff',
              },
            },
            error: {
              duration: 5000,
              style: {
                background: '#ef4444',
                color: '#fff',
              },
            }
          }}
        />
        {children}
      </AuthProvider>
    </BackgroundTasksProvider>
  );
}
