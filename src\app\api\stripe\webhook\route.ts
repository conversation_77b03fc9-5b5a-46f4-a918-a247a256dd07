// src/app/api/stripe/webhook/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/config';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature')!;

    let event;

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    console.log('Received Stripe webhook event:', event.type);

    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object);
        break;
      
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;
      
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;
      
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: 'Webhook error' }, { status: 500 });
  }
}

async function handleCheckoutSessionCompleted(session: any) {
  console.log('Checkout session completed:', session.id);
  
  const { metadata, customer_email, client_reference_id } = session;
  const planId = metadata?.planId || client_reference_id;
  
  // Enviar email de confirmación al administrador
  await sendAdminNotification({
    type: 'payment_success',
    email: customer_email,
    planId: planId,
    sessionId: session.id,
    amount: session.amount_total,
    currency: session.currency,
  });
  
  // Aquí podrías crear el usuario en Supabase automáticamente
  // await createUserInSupabase(customer_email, planId);
}

async function handleSubscriptionCreated(subscription: any) {
  console.log('Subscription created:', subscription.id);
  
  const { metadata } = subscription;
  
  await sendAdminNotification({
    type: 'subscription_created',
    email: metadata?.customerEmail,
    planId: metadata?.planId,
    subscriptionId: subscription.id,
  });
}

async function handleSubscriptionUpdated(subscription: any) {
  console.log('Subscription updated:', subscription.id);
  // Manejar cambios en la suscripción
}

async function handleSubscriptionDeleted(subscription: any) {
  console.log('Subscription deleted:', subscription.id);
  
  const { metadata } = subscription;
  
  await sendAdminNotification({
    type: 'subscription_cancelled',
    email: metadata?.customerEmail,
    planId: metadata?.planId,
    subscriptionId: subscription.id,
  });
}

async function handlePaymentSucceeded(invoice: any) {
  console.log('Payment succeeded:', invoice.id);
  // Manejar pago exitoso recurrente
}

async function handlePaymentFailed(invoice: any) {
  console.log('Payment failed:', invoice.id);
  
  await sendAdminNotification({
    type: 'payment_failed',
    email: invoice.customer_email,
    invoiceId: invoice.id,
    amount: invoice.amount_due,
  });
}

async function sendAdminNotification(data: any) {
  try {
    const notificationEmail = process.env.NOTIFICATION_EMAIL || '<EMAIL>';
    
    let subject = '';
    let content = '';
    
    switch (data.type) {
      case 'payment_success':
        subject = `💳 Pago Exitoso - OposiAI Plan ${data.planId}`;
        content = `
          <h2>🎉 Nuevo Pago Exitoso</h2>
          <p><strong>Email:</strong> ${data.email}</p>
          <p><strong>Plan:</strong> ${data.planId}</p>
          <p><strong>Monto:</strong> ${(data.amount / 100).toFixed(2)} ${data.currency.toUpperCase()}</p>
          <p><strong>Session ID:</strong> ${data.sessionId}</p>
          <p><strong>Acción requerida:</strong> Crear usuario en Supabase y asignar plan</p>
        `;
        break;
      
      case 'subscription_created':
        subject = `🔄 Nueva Suscripción - OposiAI`;
        content = `
          <h2>📅 Nueva Suscripción Creada</h2>
          <p><strong>Email:</strong> ${data.email}</p>
          <p><strong>Plan:</strong> ${data.planId}</p>
          <p><strong>Subscription ID:</strong> ${data.subscriptionId}</p>
        `;
        break;
      
      case 'subscription_cancelled':
        subject = `❌ Suscripción Cancelada - OposiAI`;
        content = `
          <h2>🚫 Suscripción Cancelada</h2>
          <p><strong>Email:</strong> ${data.email}</p>
          <p><strong>Plan:</strong> ${data.planId}</p>
          <p><strong>Subscription ID:</strong> ${data.subscriptionId}</p>
          <p><strong>Acción requerida:</strong> Desactivar acceso del usuario</p>
        `;
        break;
      
      case 'payment_failed':
        subject = `⚠️ Pago Fallido - OposiAI`;
        content = `
          <h2>💸 Pago Fallido</h2>
          <p><strong>Email:</strong> ${data.email}</p>
          <p><strong>Invoice ID:</strong> ${data.invoiceId}</p>
          <p><strong>Monto:</strong> ${(data.amount / 100).toFixed(2)}</p>
          <p><strong>Acción requerida:</strong> Contactar al usuario</p>
        `;
        break;
    }
    
    await resend.emails.send({
      from: 'OposiAI Pagos <<EMAIL>>',
      to: [notificationEmail],
      subject: subject,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          ${content}
          <hr style="margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">
            Notificación automática del sistema de pagos de OposiAI
          </p>
        </div>
      `,
    });
    
  } catch (error) {
    console.error('Error sending admin notification:', error);
  }
}
