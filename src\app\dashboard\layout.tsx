import type { Metadata } from 'next';
import '../globals.css';
import ClientLayout from '@/features/shared/components/ClientLayout';

export const metadata: Metadata = {
  title: 'Dashboard - OposiAI',
  description: 'Dashboard de OposiAI - Tu asistente inteligente para oposiciones',
};

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es">
      <body className="font-sans bg-gray-100">
        <ClientLayout>
          {children}
        </ClientLayout>
      </body>
    </html>
  );
}
