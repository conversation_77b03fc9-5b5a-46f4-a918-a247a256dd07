globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(app)/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/features/shared/components/ClientLayout.tsx":{"*":{"id":"(ssr)/./src/features/shared/components/ClientLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/page.tsx":{"*":{"id":"(ssr)/./src/app/(app)/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(marketing)/landing/page.tsx":{"*":{"id":"(ssr)/./src/app/(marketing)/landing/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(marketing)/payment/page.tsx":{"*":{"id":"(ssr)/./src/app/(marketing)/payment/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(marketing)/thank-you/page.tsx":{"*":{"id":"(ssr)/./src/app/(marketing)/thank-you/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/(marketing)/layout","static/chunks/app/(marketing)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\features\\shared\\components\\ClientLayout.tsx":{"id":"(app-pages-browser)/./src/features/shared/components/ClientLayout.tsx","name":"*","chunks":["app/(app)/layout","static/chunks/app/(app)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\app\\(app)\\page.tsx":{"id":"(app-pages-browser)/./src/app/(app)/page.tsx","name":"*","chunks":["app/(app)/page","static/chunks/app/(app)/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\app\\(marketing)\\landing\\page.tsx":{"id":"(app-pages-browser)/./src/app/(marketing)/landing/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\app\\(marketing)\\payment\\page.tsx":{"id":"(app-pages-browser)/./src/app/(marketing)/payment/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\app\\(marketing)\\thank-you\\page.tsx":{"id":"(app-pages-browser)/./src/app/(marketing)/thank-you/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\app\\layout":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\app\\(app)\\layout":[{"inlined":false,"path":"static/css/app/(app)/layout.css"}],"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\app\\(app)\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\OpsiAI con lading\\src\\app\\(marketing)\\layout":[{"inlined":false,"path":"static/css/app/(marketing)/layout.css"}]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/features/shared/components/ClientLayout.tsx":{"*":{"id":"(rsc)/./src/features/shared/components/ClientLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(app)/page.tsx":{"*":{"id":"(rsc)/./src/app/(app)/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(rsc)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(marketing)/landing/page.tsx":{"*":{"id":"(rsc)/./src/app/(marketing)/landing/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(marketing)/payment/page.tsx":{"*":{"id":"(rsc)/./src/app/(marketing)/payment/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(marketing)/thank-you/page.tsx":{"*":{"id":"(rsc)/./src/app/(marketing)/thank-you/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}