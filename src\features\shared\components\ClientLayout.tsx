'use client';

import React from 'react';
import { BackgroundTasksProvider } from '@/contexts/BackgroundTasksContext';
import AuthManager from '../../auth/components/AuthManager';
import BackgroundTasksPanel from './BackgroundTasksPanel';

interface ClientLayoutProps {
  children: React.ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <BackgroundTasksProvider>
      <AuthManager />
      <BackgroundTasksPanel />
      {children}
    </BackgroundTasksProvider>
  );
}
