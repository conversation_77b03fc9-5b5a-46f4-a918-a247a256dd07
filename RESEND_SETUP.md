# Configuración de Resend para Notificaciones por Email

## 📧 ¿Qué es Resend?

Resend es un servicio moderno de envío de emails diseñado para desarrolladores. Es confiable, fácil de configurar y tiene una API simple.

## 🚀 Configuración Paso a Paso

### 1. Crear Cuenta en Resend

1. Ve a [resend.com](https://resend.com)
2. Haz clic en "Sign Up" 
3. Crea tu cuenta con email y contraseña
4. Verifica tu email

### 2. Obtener API Key

1. Una vez logueado, ve a **API Keys** en el dashboard
2. Haz clic en **"Create API Key"**
3. Dale un nombre descriptivo: `OposiAI-Notifications`
4. Selecciona permisos: **"Sending access"**
5. Copia la API key (empieza con `re_`)

### 3. Configurar Variables de Entorno

Edita el archivo `.env.local` y reemplaza la API key placeholder:

```env
# Reemplaza esta línea:
RESEND_API_KEY=re_123456789_PLACEHOLDER_KEY_REPLACE_WITH_REAL_KEY

# Con tu API key real:
RESEND_API_KEY=re_tu_api_key_real_aqui
```

### 4. Verificar Configuración

1. Reinicia el servidor de desarrollo:
   ```bash
   npm run dev
   ```

2. Ve a la landing page: `http://localhost:3001`
3. Selecciona un plan
4. Ingresa un email de prueba
5. Envía el formulario

### 5. Verificar Email

- El email se enviará a `<EMAIL>`
- Revisa la bandeja de entrada y spam
- El email tendrá formato HTML profesional

## 🔧 Solución de Problemas

### Error: "RESEND_API_KEY no está configurada"
- Verifica que la variable esté en `.env.local`
- Reinicia el servidor después de cambiar variables de entorno

### Error: "Invalid API key"
- Verifica que la API key sea correcta
- Asegúrate de que no tenga espacios extra

### Email no llega
- Revisa la carpeta de spam
- Verifica que `<EMAIL>` sea correcto
- Revisa los logs del servidor para errores

## 📋 Flujo Completo

1. **Usuario selecciona plan** → Redirige a `/payment?plan={planId}`
2. **Usuario ingresa email** → Envía datos a `/api/notify-signup`
3. **API procesa datos** → Envía email con Resend
4. **Email <NAME_EMAIL>** → Con todos los detalles
5. **Usuario ve confirmación** → Redirige a `/thank-you`

## 💡 Próximos Pasos

Una vez que recibas el email de notificación:

1. **Crear usuario en Supabase** manualmente
2. **Asignar plan correspondiente**
3. **Enviar credenciales** al usuario
4. **Configurar límites** según el plan

## 🎨 Personalización

El email incluye:
- ✅ **Diseño profesional** con gradientes
- ✅ **Información completa** del usuario y plan
- ✅ **Lista de próximos pasos**
- ✅ **Formato responsive**
- ✅ **Versión texto plano** como fallback
