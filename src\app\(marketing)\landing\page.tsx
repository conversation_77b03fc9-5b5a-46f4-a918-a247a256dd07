// src/app/(marketing)/landing/page.tsx
'use client';

import React from 'react';
import { FiCheckCircle, FiPlayCircle, FiArrowRight, FiBox, FiUsers, FiZap } from 'react-icons/fi'; // Example icons
import { useRouter } from 'next/navigation';

// Placeholder component for video sections
const VideoPlaceholder: React.FC<{ title: string }> = ({ title }) => (
  <div className="bg-gray-200 aspect-video w-full max-w-2xl mx-auto rounded-lg flex items-center justify-center">
    <div className="text-center">
      <FiPlayCircle className="text-5xl text-gray-500 mx-auto mb-2" />
      <p className="text-gray-600">{title}</p>
    </div>
  </div>
);

const PlanCard: React.FC<{
  title: string;
  price?: string; // Optional for custom text like "Free"
  features: string[];
  ctaText: string;
  planId: string;
  bgColorClass: string;
  textColorClass: string;
  borderColorClass: string;
  router: any; // NextRouter
}> = ({ title, price, features, ctaText, planId, bgColorClass, textColorClass, borderColorClass, router }) => {
  const handleSelectPlan = () => {
    router.push(`/payment?plan=${planId}`);
  };

  return (
    <div className={`border ${borderColorClass} rounded-xl shadow-lg p-8 flex flex-col h-full ${bgColorClass}`}>
      <h3 className={`text-2xl font-bold mb-4 ${textColorClass}`}>{title}</h3>
      {price && <p className={`text-3xl font-extrabold mb-6 ${textColorClass}`}>{price}</p>}
      <ul className="space-y-3 mb-8 flex-grow">
        {features.map((feature, index) => (
          <li key={index} className={`flex items-start ${textColorClass}`}>
            <FiCheckCircle className={`w-5 h-5 mr-3 mt-1 flex-shrink-0 ${textColorClass === 'text-white' ? 'text-green-300' : 'text-green-500'}`} />
            <span>{feature}</span>
          </li>
        ))}
      </ul>
      <button
        onClick={handleSelectPlan}
        className={`w-full py-3 px-6 rounded-lg font-semibold transition-transform duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
          textColorClass === 'text-white'
            ? `bg-white ${bgColorClass.includes('purple') ? 'text-purple-700' : 'text-blue-700'} hover:bg-gray-100 focus:ring-white`
            : `${bgColorClass.includes('purple') ? 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500' : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'} text-white`
        }`}
      >
        {ctaText} <FiArrowRight className="inline ml-2" />
      </button>
    </div>
  );
};


export default function LandingPage() {
  const router = useRouter();

  const plans = [
    {
      title: 'Free',
      price: 'Gratis',
      features: [
        'Sube 1 documento',
        '5 mapas mentales a la semana',
        '30 preguntas tipo test a la semana',
        '30 flashcards a la semana',
      ],
      ctaText: 'Empezar Gratis',
      planId: 'free',
      bgColorClass: 'bg-white',
      textColorClass: 'text-gray-800',
      borderColorClass: 'border-gray-300',
    },
    {
      title: 'Usuario',
      price: '€X.XX/mes', // Placeholder price
      features: [
        'Sube 5 documentos',
        '10 mapas mentales a la semana',
        '150 preguntas tipo test a la semana',
        '150 flashcards a la semana',
      ],
      ctaText: 'Seleccionar Plan Usuario',
      planId: 'usuario',
      bgColorClass: 'bg-gradient-to-br from-blue-600 to-indigo-700',
      textColorClass: 'text-white',
      borderColorClass: 'border-blue-700',
    },
    {
      title: 'Pro',
      price: '€Y.YY/mes', // Placeholder price
      features: [
        'Sube un temario completo',
        'Planificación de estudio hasta examen mediante IA',
        'Mapas mentales ilimitados',
        'Preguntas tipo test ilimitadas',
        'Flashcards ilimitadas',
      ],
      ctaText: 'Seleccionar Plan Pro',
      planId: 'pro',
      bgColorClass: 'bg-gradient-to-br from-purple-600 to-violet-700',
      textColorClass: 'text-white',
      borderColorClass: 'border-purple-700',
    },
  ];

  const howItWorksSteps = [
    {
      title: '1. Elige tu Temario',
      description: 'Selecciona un temario predeterminado de nuestra base de datos o sube manualmente los documentos de tu oposición.',
      icon: <FiBox className="w-12 h-12 text-blue-600 mb-4" />
    },
    {
      title: '2. Selecciona el Tema',
      description: 'Escoge el tema específico que quieres preparar y enfoca tu estudio de manera eficiente.',
      icon: <FiCheckCircle className="w-12 h-12 text-blue-600 mb-4" />
    },
    {
      title: '3. Planifica tu Estudio (Modo Guiado)',
      description: 'Responde a preguntas predeterminadas para ayudarnos a entender tus necesidades y crear un borrador de tu plan.',
      icon: <FiUsers className="w-12 h-12 text-blue-600 mb-4" />
    },
    {
      title: '4. Planificación IA (Modo Pro)',
      description: 'Deja que nuestra Inteligencia Artificial diseñe un plan de estudio optimizado y personalizado para ti.',
      icon: <FiZap className="w-12 h-12 text-blue-600 mb-4" />
    },
    {
      title: '5. Empieza tu Preparación Inteligente',
      description: 'OposiAI, tu preparador personal, te ayuda a crear mapas mentales espectaculares, flashcards (con curva del olvido), tests, y resuelve tus dudas.',
      icon: <FiPlayCircle className="w-12 h-12 text-blue-600 mb-4" />
    }
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-700 text-white">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-5xl font-extrabold mb-6">
            OposiAI: Tu Preparador Personal Inteligente
          </h1>
          <p className="text-xl mb-10 max-w-3xl mx-auto">
            Transforma tu estudio para oposiciones con el poder de la IA. Crea planes de estudio, mapas mentales, tests y flashcards de forma automática y eficiente.
          </p>
          <button
            onClick={() => router.push('#plans')} // Smooth scroll to plans
            className="bg-white text-blue-700 font-bold py-3 px-8 rounded-lg text-lg hover:bg-gray-100 transition-colors duration-300 shadow-lg"
          >
            Ver Planes de Suscripción
          </button>
        </div>
      </section>

      {/* Subscription Plans Section */}
      <section id="plans" className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <h2 className="text-4xl font-bold text-center text-gray-800 mb-4">
            Elige el Plan Perfecto para Ti
          </h2>
          <p className="text-center text-gray-600 mb-16 max-w-2xl mx-auto">
            Tenemos opciones para cada tipo de opositor. Comienza gratis o desbloquea todo el potencial de OposiAI con nuestros planes avanzados.
          </p>
          <div className="grid md:grid-cols-3 gap-8">
            {plans.map((plan) => (
              <PlanCard key={plan.planId} {...plan} router={router} />
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <h2 className="text-4xl font-bold text-center text-gray-800 mb-16">
            ¿Cómo Funciona OposiAI?
          </h2>
          <div className="grid md:grid-cols-1 gap-10 mb-12">
            {howItWorksSteps.map((step, index) => (
              <div key={index} className="bg-white p-8 rounded-xl shadow-lg flex items-start space-x-6">
                <div className="flex-shrink-0">
                  {step.icon}
                </div>
                <div>
                  <h3 className="text-2xl font-semibold text-gray-800 mb-3">{step.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-12 text-center">
             <h3 className="text-2xl font-semibold text-gray-800 mb-6">Videos Explicativos</h3>
             <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                Mira cómo cada fase de OposiAI te ayuda a optimizar tu preparación.
             </p>
             <div className="grid md:grid-cols-2 gap-8">
                <VideoPlaceholder title="Video: Eligiendo y Configurando tu Temario" />
                <VideoPlaceholder title="Video: Planificación de Estudio Inteligente" />
                <VideoPlaceholder title="Video: Herramientas de Preparación IA (Mapas, Tests, Flashcards)" />
                <VideoPlaceholder title="Video: Tu Preparador Personal OposAI en Acción" />
             </div>
          </div>
        </div>
      </section>

      {/* Call to Action (optional, if not covered by header's login) */}
      <section className="py-20 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold mb-6">¿Listo para Empezar?</h2>
          <p className="text-lg mb-8 max-w-xl mx-auto">
            Únete a OposiAI y lleva tu preparación al siguiente nivel.
          </p>
          <button
            onClick={() => router.push('/login')}
            className="bg-white text-blue-700 font-bold py-3 px-10 rounded-lg text-lg hover:bg-gray-100 transition-colors duration-300 shadow-lg"
          >
            Iniciar Sesión
          </button>
        </div>
      </section>
    </div>
  );
}
