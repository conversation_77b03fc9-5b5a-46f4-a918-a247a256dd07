// src/app/api/test-stripe-connection/route.ts
import { NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/config';

export async function GET() {
  try {
    console.log('Testing Stripe connection...');
    
    // Verificar variables de entorno
    const stripeKey = process.env.STRIPE_SECRET_KEY;
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    
    console.log('Stripe key exists:', !!stripeKey);
    console.log('Webhook secret exists:', !!webhookSecret);
    console.log('Stripe key length:', stripeKey?.length || 0);
    console.log('Webhook secret length:', webhookSecret?.length || 0);

    if (!stripeKey) {
      return NextResponse.json({
        error: 'STRIPE_SECRET_KEY not found'
      }, { status: 500 });
    }

    // Hacer una llamada simple a Stripe
    const account = await stripe.accounts.retrieve();
    
    return NextResponse.json({
      success: true,
      message: 'Stripe connection successful',
      accountId: account.id,
      hasWebhookSecret: !!webhookSecret,
      stripeKeyLength: stripeKey.length,
      webhookSecretLength: webhookSecret?.length || 0
    });
  } catch (error) {
    console.error('Stripe connection error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : 'No stack trace'
    }, { status: 500 });
  }
}
