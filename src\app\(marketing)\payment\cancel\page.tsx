// src/app/(marketing)/payment/cancel/page.tsx
'use client';

import React, { Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FiXCircle, FiHome, FiArrowLeft, FiHelpCircle } from 'react-icons/fi';

function PaymentCancelContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const planId = searchParams.get('plan');

  const planNames: { [key: string]: string } = {
    usuario: 'Plan Usuario',
    pro: 'Plan Pro',
  };

  return (
    <div className="container mx-auto px-6 py-20 text-center">
      <div className="max-w-2xl mx-auto">
        <FiXCircle className="text-8xl text-orange-500 mx-auto mb-8" />
        
        <h1 className="text-4xl font-bold text-gray-800 mb-6">
          <PERSON>go Can<PERSON>
        </h1>
        
        <p className="text-xl text-gray-600 mb-8">
          No se ha realizado ningún cargo. Tu pago ha sido cancelado.
        </p>

        {planId && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 mb-8">
            <h2 className="text-lg font-semibold text-orange-800 mb-2">
              Plan seleccionado: {planNames[planId] || planId}
            </h2>
            <p className="text-orange-700">
              Puedes intentar el proceso de pago nuevamente cuando estés listo.
            </p>
          </div>
        )}

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-center justify-center space-x-2 text-blue-700 mb-3">
            <FiHelpCircle className="w-5 h-5" />
            <h3 className="text-lg font-semibold">¿Necesitas Ayuda?</h3>
          </div>
          <div className="text-blue-700 space-y-2">
            <p>Si experimentaste algún problema durante el proceso de pago:</p>
            <ul className="text-left space-y-1 mt-3">
              <li>• Verifica que tu tarjeta tenga fondos suficientes</li>
              <li>• Asegúrate de que los datos de la tarjeta sean correctos</li>
              <li>• Intenta con una tarjeta diferente o PayPal</li>
              <li>• Contacta con tu banco si el problema persiste</li>
            </ul>
          </div>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Otras Opciones</h3>
          <div className="text-gray-700 space-y-2">
            <p>• Puedes probar con el <strong>Plan Gratuito</strong> para comenzar</p>
            <p>• Contacta con nosotros si necesitas ayuda personalizada</p>
            <p>• Email: <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a></p>
          </div>
        </div>

        <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
          <button
            onClick={() => router.push(planId ? `/payment?plan=${planId}` : '/')}
            className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            <FiArrowLeft className="mr-2" /> 
            {planId ? 'Intentar de Nuevo' : 'Volver al Inicio'}
          </button>
          <button
            onClick={() => router.push('/#plans')}
            className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 bg-gray-200 text-gray-700 font-semibold rounded-lg hover:bg-gray-300 transition-colors duration-200"
          >
            <FiHome className="mr-2" /> Ver Todos los Planes
          </button>
        </div>

        <div className="mt-8">
          <p className="text-sm text-gray-500">
            ¿Prefieres empezar gratis?{' '}
            <button
              onClick={() => router.push('/payment?plan=free')}
              className="text-blue-600 hover:underline font-medium"
            >
              Prueba nuestro Plan Gratuito
            </button>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function PaymentCancelPage() {
  return (
    <Suspense fallback={
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Cargando...</p>
        </div>
      </div>
    }>
      <PaymentCancelContent />
    </Suspense>
  );
}
