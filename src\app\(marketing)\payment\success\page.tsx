// src/app/(marketing)/payment/success/page.tsx
'use client';

import React, { Suspense, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FiCheckCircle, FiHome, FiLogIn, FiMail } from 'react-icons/fi';

function PaymentSuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const planId = searchParams.get('plan');
  const [sessionDetails, setSessionDetails] = useState<any>(null);

  const planNames: { [key: string]: string } = {
    usuario: 'Plan Usuario',
    pro: 'Plan Pro',
  };

  useEffect(() => {
    // Aquí podrías verificar el estado de la sesión con Stripe si necesitas más detalles
    if (sessionId) {
      console.log('Payment successful for session:', sessionId);
    }
  }, [sessionId]);

  return (
    <div className="container mx-auto px-6 py-20 text-center">
      <div className="max-w-2xl mx-auto">
        <FiCheckCircle className="text-8xl text-green-500 mx-auto mb-8" />
        
        <h1 className="text-4xl font-bold text-gray-800 mb-6">
          ¡Pago Exitoso! 🎉
        </h1>
        
        {planId && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-green-800 mb-2">
              Suscripción Activada: {planNames[planId] || planId}
            </h2>
            <p className="text-green-700">
              Tu pago ha sido procesado exitosamente y tu suscripción está ahora activa.
            </p>
          </div>
        )}

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-center justify-center space-x-2 text-blue-700 mb-3">
            <FiMail className="w-5 h-5" />
            <h3 className="text-lg font-semibold">Próximos Pasos</h3>
          </div>
          <div className="text-blue-700 text-left space-y-2">
            <p>✅ <strong>Pago confirmado</strong> - Tu suscripción está activa</p>
            <p>📧 <strong>Email de confirmación</strong> - Recibirás un email con los detalles</p>
            <p>👤 <strong>Creación de cuenta</strong> - Nuestro equipo creará tu perfil en las próximas horas</p>
            <p>🔑 <strong>Credenciales de acceso</strong> - Te enviaremos tus datos de login por email</p>
          </div>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Información Importante</h3>
          <div className="text-gray-700 text-sm space-y-2">
            <p>• Tu suscripción se renovará automáticamente cada mes</p>
            <p>• Puedes cancelar en cualquier momento desde tu panel de usuario</p>
            <p>• El acceso completo estará disponible una vez que recibas tus credenciales</p>
            <p>• Si tienes preguntas, contacta con nosotros en{' '}
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>

        <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
          <button
            onClick={() => router.push('/')}
            className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            <FiHome className="mr-2" /> Volver al Inicio
          </button>
          <button
            onClick={() => router.push('/login')}
            className="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 bg-gray-200 text-gray-700 font-semibold rounded-lg hover:bg-gray-300 transition-colors duration-200"
          >
            <FiLogIn className="mr-2" /> Ir a Iniciar Sesión
          </button>
        </div>

        {sessionId && (
          <div className="mt-8 text-xs text-gray-500">
            <p>ID de Transacción: {sessionId}</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Verificando pago...</p>
        </div>
      </div>
    }>
      <PaymentSuccessContent />
    </Suspense>
  );
}
