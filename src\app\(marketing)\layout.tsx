// src/app/(marketing)/layout.tsx
import { FiLogIn, FiExternalLink } from 'react-icons/fi'; // For icons

export default function MarketingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="bg-gray-100 text-gray-900 flex flex-col min-h-screen">
        <header className="bg-white shadow-md sticky top-0 z-50">
          <nav className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
            <a href="/landing" className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:opacity-80 transition-opacity">
              OposiAI
            </a>
            <div>
              <a
                href="/login"
                className="inline-flex items-center px-5 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 hover:scale-105"
              >
                <FiLogIn className="mr-2 -ml-1 h-4 w-4" />
                Iniciar Sesión
              </a>
            </div>
          </nav>
        </header>

        <main className="flex-grow">
          {children}
        </main>

        <footer className="bg-gray-800 text-gray-300 border-t border-gray-700 mt-auto">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
              <div>
                <h5 className="text-xl font-bold text-white mb-3">OposiAI</h5>
                <p className="text-sm">
                  Tu asistente inteligente para la preparación de oposiciones. Optimizamos tu estudio con IA.
                </p>
              </div>
              <div>
                <h5 className="text-lg font-semibold text-white mb-3">Enlaces Rápidos</h5>
                <ul className="space-y-2 text-sm">
                  <li><a href="/landing#plans" className="hover:text-blue-400 transition-colors">Planes</a></li>
                  <li><a href="/landing#how-it-works" className="hover:text-blue-400 transition-colors">Cómo Funciona</a></li>
                  {/* Add more links as needed */}
                </ul>
              </div>
              <div>
                <h5 className="text-lg font-semibold text-white mb-3">Legal</h5>
                <ul className="space-y-2 text-sm">
                  <li><a href="#" className="hover:text-blue-400 transition-colors">Términos de Servicio</a></li>
                  <li><a href="#" className="hover:text-blue-400 transition-colors">Política de Privacidad</a></li>
                  <li><a href="mailto:<EMAIL>" className="hover:text-blue-400 transition-colors flex items-center">
                    Contacto <FiExternalLink className="ml-1.5 h-3 w-3"/>
                  </a></li>
                </ul>
              </div>
            </div>
            <div className="border-t border-gray-700 pt-8 text-center text-sm">
              <p>&copy; {new Date().getFullYear()} OposiAI. Todos los derechos reservados.</p>
              <p className="mt-1">Una herramienta para opositores, por opositores.</p>
            </div>
          </div>
        </footer>
    </div>
  );
}
