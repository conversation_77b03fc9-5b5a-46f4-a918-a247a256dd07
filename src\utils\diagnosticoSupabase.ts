import { supabase } from '../lib/supabase/supabaseClient';
import { obtenerUsuarioActual } from '../lib/supabase/authService';

/**
 * Función de diagnóstico para verificar colecciones y flashcards en Supabase
 */
export async function diagnosticarColeccionesFlashcards() {
  console.log('=== DIAGNÓSTICO DE COLECCIONES Y FLASHCARDS ===');
  
  try {
    // 1. Verificar usuario actual
    const { user, error: userError } = await obtenerUsuarioActual();
    
    if (userError) {
      console.error('❌ Error al obtener usuario:', userError);
      return;
    }
    
    if (!user) {
      console.error('❌ No hay usuario autenticado');
      return;
    }
    
    console.log('✅ Usuario autenticado:', user.id, user.email);
    
    // 2. Obtener todas las colecciones del usuario
    const { data: colecciones, error: coleccionesError } = await supabase
      .from('colecciones_flashcards')
      .select('*')
      .eq('user_id', user.id)
      .order('creado_en', { ascending: false });
    
    if (coleccionesError) {
      console.error('❌ Error al obtener colecciones:', coleccionesError);
      return;
    }
    
    console.log(`\n📚 COLECCIONES ENCONTRADAS: ${colecciones?.length || 0}`);
    
    if (!colecciones || colecciones.length === 0) {
      console.log('⚠️ No se encontraron colecciones para este usuario');
      return;
    }
    
    // 3. Mostrar información de cada colección
    for (const coleccion of colecciones) {
      console.log(`\n--- COLECCIÓN: "${coleccion.titulo}" ---`);
      console.log(`ID: ${coleccion.id}`);
      console.log(`Descripción: ${coleccion.descripcion || 'Sin descripción'}`);
      console.log(`Creada: ${new Date(coleccion.creado_en).toLocaleString()}`);
      console.log(`User ID: ${coleccion.user_id}`);
      
      // Verificar flashcards de esta colección
      const { data: flashcards, error: flashcardsError } = await supabase
        .from('flashcards')
        .select('*')
        .eq('coleccion_id', coleccion.id)
        .order('creado_en', { ascending: true });
      
      if (flashcardsError) {
        console.error(`❌ Error al obtener flashcards para "${coleccion.titulo}":`, flashcardsError);
        continue;
      }
      
      console.log(`📝 Flashcards: ${flashcards?.length || 0}`);
      
      if (flashcards && flashcards.length > 0) {
        console.log('   Primeras 3 flashcards:');
        flashcards.slice(0, 3).forEach((fc, index) => {
          console.log(`   ${index + 1}. "${fc.pregunta.substring(0, 50)}..."`);
        });
      }
      
      // Verificar progreso de flashcards
      if (flashcards && flashcards.length > 0) {
        const { data: progresos, error: progresosError } = await supabase
          .from('progreso_flashcards')
          .select('*')
          .in('flashcard_id', flashcards.map(fc => fc.id));
        
        if (progresosError) {
          console.error(`❌ Error al obtener progreso para "${coleccion.titulo}":`, progresosError);
        } else {
          console.log(`📊 Registros de progreso: ${progresos?.length || 0}`);
        }
      }
    }
    
    // 4. Buscar específicamente "Tema 1 Constitución"
    console.log('\n🔍 BÚSQUEDA ESPECÍFICA: "Tema 1 Constitución"');
    
    const coleccionConstitucion = colecciones.find(c => 
      c.titulo.toLowerCase().includes('constitución') || 
      c.titulo.toLowerCase().includes('constitucion') ||
      c.titulo.toLowerCase().includes('tema 1')
    );
    
    if (coleccionConstitucion) {
      console.log('✅ Colección encontrada:', coleccionConstitucion.titulo);
      console.log('ID:', coleccionConstitucion.id);
      
      // Verificar flashcards específicamente
      const { data: flashcardsConstitucion, error } = await supabase
        .from('flashcards')
        .select('*')
        .eq('coleccion_id', coleccionConstitucion.id);
      
      if (error) {
        console.error('❌ Error al obtener flashcards de Constitución:', error);
      } else {
        console.log(`📝 Flashcards en Constitución: ${flashcardsConstitucion?.length || 0}`);
        
        if (flashcardsConstitucion && flashcardsConstitucion.length > 0) {
          console.log('Todas las flashcards:');
          flashcardsConstitucion.forEach((fc, index) => {
            console.log(`${index + 1}. ID: ${fc.id}`);
            console.log(`   Pregunta: "${fc.pregunta.substring(0, 100)}..."`);
            console.log(`   Creada: ${new Date(fc.creado_en).toLocaleString()}`);
          });
        } else {
          console.log('⚠️ No se encontraron flashcards en esta colección');
        }
      }
    } else {
      console.log('❌ No se encontró colección relacionada con "Constitución"');
      console.log('Colecciones disponibles:');
      colecciones.forEach(c => console.log(`- "${c.titulo}"`));
    }
    
    // 5. Verificar integridad de datos
    console.log('\n🔧 VERIFICACIÓN DE INTEGRIDAD');
    
    // Verificar flashcards huérfanas (sin colección)
    const { data: flashcardsHuerfanas, error: huerfanasError } = await supabase
      .from('flashcards')
      .select('id, coleccion_id, pregunta')
      .not('coleccion_id', 'in', `(${colecciones.map(c => `'${c.id}'`).join(',')})`);
    
    if (huerfanasError) {
      console.error('❌ Error al verificar flashcards huérfanas:', huerfanasError);
    } else if (flashcardsHuerfanas && flashcardsHuerfanas.length > 0) {
      console.log(`⚠️ Flashcards huérfanas encontradas: ${flashcardsHuerfanas.length}`);
      flashcardsHuerfanas.forEach(fc => {
        console.log(`- ID: ${fc.id}, Colección: ${fc.coleccion_id}, Pregunta: "${fc.pregunta.substring(0, 50)}..."`);
      });
    } else {
      console.log('✅ No se encontraron flashcards huérfanas');
    }
    
  } catch (error) {
    console.error('❌ Error general en diagnóstico:', error);
  }
  
  console.log('\n=== FIN DEL DIAGNÓSTICO ===');
}

/**
 * Función específica para buscar una colección por nombre
 */
export async function buscarColeccionPorNombre(nombre: string) {
  try {
    const { user } = await obtenerUsuarioActual();
    if (!user) return null;

    const { data, error } = await supabase
      .from('colecciones_flashcards')
      .select('*')
      .eq('user_id', user.id)
      .ilike('titulo', `%${nombre}%`);

    if (error) {
      console.error('Error al buscar colección:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error en búsqueda:', error);
    return null;
  }
}

/**
 * Función específica para diagnosticar la colección "Tema 1 constitución"
 */
export async function diagnosticarColeccionConstitucion() {
  console.log('=== DIAGNÓSTICO ESPECÍFICO: TEMA 1 CONSTITUCIÓN ===');

  try {
    const { user } = await obtenerUsuarioActual();
    if (!user) {
      console.error('❌ No hay usuario autenticado');
      return;
    }

    // ID específico de la colección según el diagnóstico anterior
    const coleccionId = 'c25c593e-8960-47e3-9db6-c5b7849d4553';

    console.log(`🔍 Analizando colección ID: ${coleccionId}`);

    // 1. Verificar la colección
    const { data: coleccion, error: coleccionError } = await supabase
      .from('colecciones_flashcards')
      .select('*')
      .eq('id', coleccionId)
      .eq('user_id', user.id)
      .single();

    if (coleccionError) {
      console.error('❌ Error al obtener colección:', coleccionError);
      return;
    }

    console.log('✅ Colección encontrada:', coleccion.titulo);

    // 2. Obtener TODAS las flashcards directamente
    const { data: todasFlashcards, error: flashcardsError } = await supabase
      .from('flashcards')
      .select('*')
      .eq('coleccion_id', coleccionId)
      .order('creado_en', { ascending: true });

    if (flashcardsError) {
      console.error('❌ Error al obtener flashcards:', flashcardsError);
      return;
    }

    console.log(`📝 Total flashcards en BD: ${todasFlashcards?.length || 0}`);

    // 3. Obtener progreso de todas las flashcards
    const { data: todosProgresos, error: progresosError } = await supabase
      .from('progreso_flashcards')
      .select('*')
      .in('flashcard_id', todasFlashcards?.map(fc => fc.id) || []);

    if (progresosError) {
      console.error('❌ Error al obtener progresos:', progresosError);
    } else {
      console.log(`📊 Total registros de progreso: ${todosProgresos?.length || 0}`);
    }

    // 4. Analizar flashcards sin progreso
    const flashcardsSinProgreso = todasFlashcards?.filter(fc =>
      !todosProgresos?.some(p => p.flashcard_id === fc.id)
    ) || [];

    console.log(`⚠️ Flashcards SIN progreso: ${flashcardsSinProgreso.length}`);

    if (flashcardsSinProgreso.length > 0) {
      console.log('Primeras 5 flashcards sin progreso:');
      flashcardsSinProgreso.slice(0, 5).forEach((fc, index) => {
        console.log(`${index + 1}. ID: ${fc.id}`);
        console.log(`   Pregunta: "${fc.pregunta.substring(0, 80)}..."`);
      });
    }

    // 5. Simular la función obtenerFlashcardsParaEstudiar
    console.log('\n🧪 SIMULANDO obtenerFlashcardsParaEstudiar():');

    const ahora = new Date();
    const hoy = new Date(ahora.getFullYear(), ahora.getMonth(), ahora.getDate());

    const flashcardsConProgreso = todasFlashcards?.map(flashcard => {
      const progreso = todosProgresos?.find(p => p.flashcard_id === flashcard.id);

      if (!progreso) {
        return {
          ...flashcard,
          debeEstudiar: true,
          progreso: null
        };
      }

      const proximaRevision = new Date(progreso.proxima_revision);
      const proximaRevisionSinHora = new Date(
        proximaRevision.getFullYear(),
        proximaRevision.getMonth(),
        proximaRevision.getDate()
      );
      const debeEstudiar = proximaRevisionSinHora <= hoy;

      return {
        ...flashcard,
        debeEstudiar,
        progreso: {
          factor_facilidad: progreso.factor_facilidad,
          intervalo: progreso.intervalo,
          repeticiones: progreso.repeticiones,
          estado: progreso.estado,
          proxima_revision: progreso.proxima_revision,
        },
      };
    }) || [];

    console.log(`📋 Flashcards procesadas: ${flashcardsConProgreso.length}`);
    console.log(`🎯 Flashcards que DEBEN estudiarse: ${flashcardsConProgreso.filter(fc => fc.debeEstudiar).length}`);
    console.log(`⏰ Flashcards programadas para hoy: ${flashcardsConProgreso.filter(fc => fc.debeEstudiar && fc.progreso).length}`);
    console.log(`🆕 Flashcards nuevas (sin progreso): ${flashcardsConProgreso.filter(fc => fc.debeEstudiar && !fc.progreso).length}`);

    // 6. Verificar estadísticas
    console.log('\n📊 VERIFICANDO ESTADÍSTICAS:');

    const estadisticas = {
      total: flashcardsConProgreso.length,
      nuevas: flashcardsConProgreso.filter(fc => !fc.progreso).length,
      aprendiendo: flashcardsConProgreso.filter(fc => fc.progreso?.estado === 'aprendiendo').length,
      repasando: flashcardsConProgreso.filter(fc => fc.progreso?.estado === 'repasando').length,
      aprendidas: flashcardsConProgreso.filter(fc => fc.progreso?.estado === 'aprendido').length,
      paraHoy: flashcardsConProgreso.filter(fc => fc.debeEstudiar).length
    };

    console.log('Estadísticas calculadas:');
    console.log(`- Total: ${estadisticas.total}`);
    console.log(`- Nuevas: ${estadisticas.nuevas}`);
    console.log(`- Aprendiendo: ${estadisticas.aprendiendo}`);
    console.log(`- Repasando: ${estadisticas.repasando}`);
    console.log(`- Aprendidas: ${estadisticas.aprendidas}`);
    console.log(`- Para hoy: ${estadisticas.paraHoy}`);

  } catch (error) {
    console.error('❌ Error en diagnóstico específico:', error);
  }

  console.log('\n=== FIN DIAGNÓSTICO ESPECÍFICO ===');
}
