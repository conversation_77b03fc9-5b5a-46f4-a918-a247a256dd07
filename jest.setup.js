// jest.setup.js
// Global setup for Jest tests can go here.
// For example, setting up environment variables:
// process.env.MY_VARIABLE = 'test_value';

// You can also set up global mocks here if necessary.
// jest.mock('some-module', () => ({
//   ...jest.requireActual('some-module'),
//   someFunction: jest.fn(),
// }));

// For Next.js, you might want to mock next/router or other Next.js specific modules
// if they are used by the code under test and not handled by your jest.config.js
// or specific test files.

// Example: Mocking environment variables needed by Supabase client or other services
process.env.NEXT_PUBLIC_SUPABASE_URL = 'http://localhost:54321';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';
process.env.GEMINI_API_KEY = 'test-gemini-key';

// If your tests involve fetch, you might want to ensure it's available in the JSDOM environment
// or use a mocking library like 'jest-fetch-mock'.
// For Next.js 13+ with Route Handlers, 'fetch' is generally available.

// Silence console.error and console.warn during tests to keep output clean,
// but ensure you are checking for expected errors in your tests.
// You can create a mock implementation:
/*
let originalConsoleError;
let originalConsoleWarn;

beforeAll(() => {
  originalConsoleError = console.error;
  originalConsoleWarn = console.warn;
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});
*/

// Ensure that any async operations complete before tests exit
// if they are not explicitly awaited in the tests.
// (This is usually handled by Jest's async test support with async/await or Promises)

// console.log('Jest global setup loaded.');
