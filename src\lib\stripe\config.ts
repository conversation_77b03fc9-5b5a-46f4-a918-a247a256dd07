// src/lib/stripe/config.ts
import Stripe from 'stripe';

// Configuración del servidor Stripe
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
});

// Configuración de planes
export const PLANS = {
  free: {
    id: 'free',
    name: 'Plan Gratis',
    price: 0,
    stripePriceId: null,
    features: [
      'Sube 1 documento',
      '5 mapas mentales a la semana',
      '30 preguntas tipo test a la semana',
      '30 flashcards a la semana',
    ],
    limits: {
      documents: 1,
      mindMapsPerWeek: 5,
      testsPerWeek: 30,
      flashcardsPerWeek: 30,
    }
  },
  usuario: {
    id: 'usuario',
    name: 'Plan Usuario',
    price: 999, // En centavos (€9.99)
    stripeProductId: 'prod_SR65BdKdek1OXd', // ID del producto en Stripe
    stripePriceId: null, // Se creará dinámicamente
    features: [
      'Sube 5 documentos',
      '10 mapas mentales a la semana',
      '150 preguntas tipo test a la semana',
      '150 flashcards a la semana',
    ],
    limits: {
      documents: 5,
      mindMapsPerWeek: 10,
      testsPerWeek: 150,
      flashcardsPerWeek: 150,
    }
  },
  pro: {
    id: 'pro',
    name: 'Plan Pro',
    price: 1999, // En centavos (€19.99)
    stripeProductId: 'prod_SR66U2G7bVJqu3', // ID del producto en Stripe
    stripePriceId: null, // Se creará dinámicamente
    features: [
      'Sube un temario completo',
      'Planificación de estudio hasta examen mediante IA',
      'Mapas mentales ilimitados',
      'Preguntas tipo test ilimitadas',
      'Flashcards ilimitadas',
    ],
    limits: {
      documents: -1, // Ilimitado
      mindMapsPerWeek: -1, // Ilimitado
      testsPerWeek: -1, // Ilimitado
      flashcardsPerWeek: -1, // Ilimitado
    }
  }
} as const;

export type PlanId = keyof typeof PLANS;

// Función para obtener plan por ID
export function getPlanById(planId: string): typeof PLANS[PlanId] | null {
  return PLANS[planId as PlanId] || null;
}

// Función para validar si un plan es válido
export function isValidPlan(planId: string): planId is PlanId {
  return planId in PLANS;
}

// URLs de la aplicación
export const APP_URLS = {
  success: `${process.env.NEXT_PUBLIC_APP_URL}/payment/success`,
  cancel: `${process.env.NEXT_PUBLIC_APP_URL}/payment/cancel`,
  webhook: `${process.env.NEXT_PUBLIC_APP_URL}/api/stripe/webhook`,
} as const;
