// src/app/api/notify-signup/route.ts
import { NextResponse } from 'next/server';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: Request) {
  try {
    const { email, plan, planName } = await request.json();

    if (!email || !plan || !planName) {
      return NextResponse.json({
        message: 'Faltan datos: email, plan y planName son requeridos.'
      }, { status: 400 });
    }

    // Validar que el email tenga formato correcto
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        message: 'El formato del email no es válido.'
      }, { status: 400 });
    }

    // Verificar que las variables de entorno estén configuradas
    if (!process.env.RESEND_API_KEY) {
      console.error('RESEND_API_KEY no está configurada');
      return NextResponse.json({
        message: 'Error de configuración del servidor: RESEND_API_KEY no configurada.'
      }, { status: 500 });
    }

    // Log para debugging (sin mostrar la key completa)
    const apiKeyPrefix = process.env.RESEND_API_KEY.substring(0, 8);
    console.log('Using Resend API key starting with:', apiKeyPrefix);

    const notificationEmail = process.env.NOTIFICATION_EMAIL || '<EMAIL>';
    console.log('Sending notification to:', notificationEmail);

    // Crear el contenido del email
    const emailSubject = `🚀 Nueva Solicitud de Suscripción OposiAI: Plan ${planName}`;

    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
          <h1 style="color: white; margin: 0; font-size: 28px;">🚀 Nueva Solicitud OposiAI</h1>
        </div>

        <div style="background: #f8f9fa; padding: 25px; border-radius: 8px; border-left: 4px solid #667eea;">
          <h2 style="color: #333; margin-top: 0;">Detalles de la Solicitud</h2>

          <div style="background: white; padding: 20px; border-radius: 6px; margin: 15px 0;">
            <p style="margin: 8px 0;"><strong>📧 Email del Usuario:</strong> <span style="color: #667eea;">${email}</span></p>
            <p style="margin: 8px 0;"><strong>📋 Plan Seleccionado:</strong> <span style="color: #764ba2; font-weight: bold;">${planName}</span></p>
            <p style="margin: 8px 0;"><strong>🔖 Plan ID:</strong> <code style="background: #f1f3f4; padding: 2px 6px; border-radius: 3px;">${plan}</code></p>
            <p style="margin: 8px 0;"><strong>⏰ Fecha:</strong> ${new Date().toLocaleString('es-ES', { timeZone: 'Europe/Madrid' })}</p>
          </div>
        </div>

        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1976d2; margin-top: 0;">📝 Próximos Pasos</h3>
          <ol style="color: #333; line-height: 1.6;">
            <li>Crear el perfil del usuario en Supabase</li>
            <li>Asignar el plan correspondiente</li>
            <li>Enviar email de bienvenida con credenciales</li>
            <li>Configurar límites según el plan seleccionado</li>
          </ol>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f5f5f5; border-radius: 8px;">
          <p style="color: #666; margin: 0; font-size: 14px;">
            Este email fue generado automáticamente por el sistema de notificaciones de OposiAI
          </p>
        </div>
      </div>
    `;

    const emailText = `
Nueva Solicitud de Suscripción OposiAI

Detalles de la Solicitud:
- Email del Usuario: ${email}
- Plan Seleccionado: ${planName}
- Plan ID: ${plan}
- Fecha: ${new Date().toLocaleString('es-ES', { timeZone: 'Europe/Madrid' })}

Próximos Pasos:
1. Crear el perfil del usuario en Supabase
2. Asignar el plan correspondiente
3. Enviar email de bienvenida con credenciales
4. Configurar límites según el plan seleccionado

Este email fue generado automáticamente por el sistema de notificaciones de OposiAI.
    `;

    // Enviar el email usando Resend
    console.log('Attempting to send email with Resend...');

    const emailPayload = {
      from: 'OposiAI Notificaciones <<EMAIL>>',
      to: [notificationEmail],
      subject: emailSubject,
      html: emailHtml,
      text: emailText,
    };

    console.log('Email payload:', {
      from: emailPayload.from,
      to: emailPayload.to,
      subject: emailPayload.subject
    });

    const { data, error } = await resend.emails.send(emailPayload);

    if (error) {
      console.error('Error enviando email con Resend:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));

      // Proporcionar mensaje de error más específico
      let errorMessage = 'Error al enviar la notificación por email.';
      if (error.message) {
        errorMessage += ` Detalle: ${error.message}`;
      }

      return NextResponse.json({
        message: errorMessage,
        error: error.message || 'Unknown error'
      }, { status: 500 });
    }

    console.log('Email enviado exitosamente:', data);

    return NextResponse.json({
      message: 'Solicitud recibida. Se ha enviado la notificación por email.',
      emailId: data?.id
    }, { status: 200 });

  } catch (error) {
    console.error('Error en /api/notify-signup:', error);

    let errorMessage = 'Error al procesar la solicitud.';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    console.error('Returning error message:', errorMessage);
    return NextResponse.json({
      message: errorMessage
    }, { status: 500 });
  }
}
