// src/app/api/notify-signup/route.ts
import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: Request) {
  try {
    const { email, plan, planName } = await request.json();

    if (!email || !plan || !planName) {
      return NextResponse.json({ message: 'Faltan datos: email, plan y planName son requeridos.' }, { status: 400 });
    }

    // Create a test account for Ethereal
    // In a real app, use process.env for SMTP config
    let testAccount = await nodemailer.createTestAccount();
    // Log Ethereal credentials to the console when the subtask runs.
    // This is important for the main agent to be able to report them.
    console.log('ETHEREAL_USER:', testAccount.user);
    console.log('ETHEREAL_PASS:', testAccount.pass);


    const transporter = nodemailer.createTransport({
      host: 'smtp.ethereal.email', // Ethereal SMTP
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: testAccount.user, // Generated Ethereal user
        pass: testAccount.pass, // Generated Ethereal password
      },
    });

    const mailOptions = {
      from: `"OposiAI Notificaciones" <${testAccount.user}>`, // sender address
      to: '<EMAIL>', // list of receivers
      subject: `Nueva Solicitud de Suscripción OposiAI: Plan ${planName}`, // Subject line
      text: `Un usuario ha solicitado una suscripción:

Email: ${email}
Plan ID: ${plan}
Plan Nombre: ${planName}

Por favor, procede a crear su perfil.`, // plain text body
      html: `<p>Un usuario ha solicitado una suscripción:</p>
             <ul>
               <li><strong>Email:</strong> ${email}</li>
               <li><strong>Plan ID:</strong> ${plan}</li>
               <li><strong>Plan Nombre:</strong> ${planName}</li>
             </ul>
             <p>Por favor, procede a crear su perfil.</p>`, // html body
    };

    let info = await transporter.sendMail(mailOptions);

    // Log Ethereal message preview URL to the console.
    const previewUrl = nodemailer.getTestMessageUrl(info);
    console.log('ETHEREAL_PREVIEW_URL:', previewUrl);

    return NextResponse.json({
        message: 'Solicitud recibida. Se ha enviado la notificación.',
        etherealPreviewUrl: previewUrl // Also return it in response for easier testing
    }, { status: 200 });

  } catch (error) {
    console.error('Error en /api/notify-signup:', error);
    // Check if error is a known type, e.g. from nodemailer
    let errorMessage = 'Error al procesar la solicitud.';
    if (error instanceof Error) {
        errorMessage = error.message;
    }
    // Log the specific error message that will be returned
    console.error('Returning error message:', errorMessage);
    return NextResponse.json({ message: errorMessage }, { status: 500 });
  }
}
